/*! tailwindcss v4.1.4 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-scroll-snap-strictness:proximity;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-ordinal:initial;--tw-slashed-zero:initial;--tw-numeric-figure:initial;--tw-numeric-spacing:initial;--tw-numeric-fraction:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-duration:initial;--tw-ease:initial}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-400:oklch(70.4% .191 22.216);--color-red-500:oklch(63.7% .237 25.331);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-yellow-100:oklch(97.3% .071 103.193);--color-yellow-300:oklch(90.5% .182 98.111);--color-yellow-400:oklch(85.2% .199 91.936);--color-yellow-700:oklch(55.4% .135 66.442);--color-yellow-800:oklch(47.6% .114 61.907);--color-yellow-900:oklch(42.1% .095 57.708);--color-green-100:oklch(96.2% .044 156.743);--color-green-400:oklch(79.2% .209 151.711);--color-green-500:oklch(72.3% .219 149.579);--color-green-600:oklch(62.7% .194 149.214);--color-green-700:oklch(52.7% .154 150.069);--color-blue-100:oklch(93.2% .032 255.585);--color-blue-400:oklch(70.7% .165 254.624);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-indigo-50:oklch(96.2% .018 272.314);--color-indigo-100:oklch(93% .034 272.788);--color-indigo-200:oklch(87% .065 274.039);--color-indigo-300:oklch(78.5% .115 274.713);--color-indigo-400:oklch(67.3% .182 276.935);--color-indigo-500:oklch(58.5% .233 277.117);--color-indigo-600:oklch(51.1% .262 276.966);--color-indigo-700:oklch(45.7% .24 277.023);--color-indigo-800:oklch(39.8% .195 277.366);--color-indigo-900:oklch(35.9% .144 278.697);--color-pink-600:oklch(59.2% .249 .584);--color-pink-700:oklch(52.5% .223 3.958);--color-gray-50:oklch(98.5% .002 247.839);--color-gray-100:oklch(96.7% .003 264.542);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-300:oklch(87.2% .01 258.338);--color-gray-400:oklch(70.7% .022 261.325);--color-gray-500:oklch(55.1% .027 264.364);--color-gray-600:oklch(44.6% .03 256.802);--color-gray-700:oklch(37.3% .034 259.733);--color-gray-800:oklch(27.8% .033 256.848);--color-gray-900:oklch(21% .034 264.665);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-sm:24rem;--container-md:28rem;--container-lg:32rem;--container-xl:36rem;--container-2xl:42rem;--container-3xl:48rem;--container-4xl:56rem;--container-5xl:64rem;--container-7xl:80rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height: 1.5 ;--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--text-3xl:1.875rem;--text-3xl--line-height: 1.2 ;--text-4xl:2.25rem;--text-4xl--line-height:calc(2.5/2.25);--text-5xl:3rem;--text-5xl--line-height:1;--font-weight-light:300;--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--tracking-widest:.1em;--leading-tight:1.25;--leading-relaxed:1.625;--radius-xs:.125rem;--radius-sm:.25rem;--radius-md:.375rem;--radius-lg:.5rem;--radius-xl:.75rem;--radius-2xl:1rem;--ease-in:cubic-bezier(.4,0,1,1);--ease-out:cubic-bezier(0,0,.2,1);--ease-in-out:cubic-bezier(.4,0,.2,1);--animate-spin:spin 1s linear infinite;--animate-ping:ping 1s cubic-bezier(0,0,.2,1)infinite;--animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono)}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}:where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light]{color-scheme:light;--color-base-100:oklch(100% 0 0);--color-base-200:oklch(98% 0 0);--color-base-300:oklch(95% 0 0);--color-base-content:oklch(21% .006 285.885);--color-primary:oklch(45% .24 277.023);--color-primary-content:oklch(93% .034 272.788);--color-secondary:oklch(65% .241 354.308);--color-secondary-content:oklch(94% .028 342.258);--color-accent:oklch(77% .152 181.912);--color-accent-content:oklch(38% .063 188.416);--color-neutral:oklch(14% .005 285.823);--color-neutral-content:oklch(92% .004 286.32);--color-info:oklch(74% .16 232.661);--color-info-content:oklch(29% .066 243.157);--color-success:oklch(76% .177 163.223);--color-success-content:oklch(37% .077 168.94);--color-warning:oklch(82% .189 84.429);--color-warning-content:oklch(41% .112 45.904);--color-error:oklch(71% .194 13.428);--color-error-content:oklch(27% .105 12.094);--radius-selector:.5rem;--radius-field:.25rem;--radius-box:.5rem;--size-selector:.25rem;--size-field:.25rem;--border:1px;--depth:1;--noise:0}@media (prefers-color-scheme:dark){:root{color-scheme:dark;--color-base-100:oklch(25.33% .016 252.42);--color-base-200:oklch(23.26% .014 253.1);--color-base-300:oklch(21.15% .012 254.09);--color-base-content:oklch(97.807% .029 256.847);--color-primary:oklch(58% .233 277.117);--color-primary-content:oklch(96% .018 272.314);--color-secondary:oklch(65% .241 354.308);--color-secondary-content:oklch(94% .028 342.258);--color-accent:oklch(77% .152 181.912);--color-accent-content:oklch(38% .063 188.416);--color-neutral:oklch(14% .005 285.823);--color-neutral-content:oklch(92% .004 286.32);--color-info:oklch(74% .16 232.661);--color-info-content:oklch(29% .066 243.157);--color-success:oklch(76% .177 163.223);--color-success-content:oklch(37% .077 168.94);--color-warning:oklch(82% .189 84.429);--color-warning-content:oklch(41% .112 45.904);--color-error:oklch(71% .194 13.428);--color-error-content:oklch(27% .105 12.094);--radius-selector:.5rem;--radius-field:.25rem;--radius-box:.5rem;--size-selector:.25rem;--size-field:.25rem;--border:1px;--depth:1;--noise:0}}:root:has(input.theme-controller[value=light]:checked),[data-theme=light]{color-scheme:light;--color-base-100:oklch(100% 0 0);--color-base-200:oklch(98% 0 0);--color-base-300:oklch(95% 0 0);--color-base-content:oklch(21% .006 285.885);--color-primary:oklch(45% .24 277.023);--color-primary-content:oklch(93% .034 272.788);--color-secondary:oklch(65% .241 354.308);--color-secondary-content:oklch(94% .028 342.258);--color-accent:oklch(77% .152 181.912);--color-accent-content:oklch(38% .063 188.416);--color-neutral:oklch(14% .005 285.823);--color-neutral-content:oklch(92% .004 286.32);--color-info:oklch(74% .16 232.661);--color-info-content:oklch(29% .066 243.157);--color-success:oklch(76% .177 163.223);--color-success-content:oklch(37% .077 168.94);--color-warning:oklch(82% .189 84.429);--color-warning-content:oklch(41% .112 45.904);--color-error:oklch(71% .194 13.428);--color-error-content:oklch(27% .105 12.094);--radius-selector:.5rem;--radius-field:.25rem;--radius-box:.5rem;--size-selector:.25rem;--size-field:.25rem;--border:1px;--depth:1;--noise:0}:root:has(input.theme-controller[value=dark]:checked),[data-theme=dark]{color-scheme:dark;--color-base-100:oklch(25.33% .016 252.42);--color-base-200:oklch(23.26% .014 253.1);--color-base-300:oklch(21.15% .012 254.09);--color-base-content:oklch(97.807% .029 256.847);--color-primary:oklch(58% .233 277.117);--color-primary-content:oklch(96% .018 272.314);--color-secondary:oklch(65% .241 354.308);--color-secondary-content:oklch(94% .028 342.258);--color-accent:oklch(77% .152 181.912);--color-accent-content:oklch(38% .063 188.416);--color-neutral:oklch(14% .005 285.823);--color-neutral-content:oklch(92% .004 286.32);--color-info:oklch(74% .16 232.661);--color-info-content:oklch(29% .066 243.157);--color-success:oklch(76% .177 163.223);--color-success-content:oklch(37% .077 168.94);--color-warning:oklch(82% .189 84.429);--color-warning-content:oklch(41% .112 45.904);--color-error:oklch(71% .194 13.428);--color-error-content:oklch(27% .105 12.094);--radius-selector:.5rem;--radius-field:.25rem;--radius-box:.5rem;--size-selector:.25rem;--size-field:.25rem;--border:1px;--depth:1;--noise:0}:root:has(.modal-open,.modal[open],.modal:target,.modal-toggle:checked,.drawer:not([class*=drawer-open])>.drawer-toggle:checked){overflow:hidden}:root,[data-theme]{background-color:var(--root-bg,var(--color-base-100));color:var(--color-base-content)}:root{scrollbar-color:currentColor #0000}@supports (color:color-mix(in lab,red,red)){:root{scrollbar-color:color-mix(in oklch,currentColor 35%,#0000)#0000}}@property --radialprogress{syntax: "<percentage>"; inherits: true; initial-value: 0%;}:where(:root:has(.modal-open,.modal[open],.modal:target,.modal-toggle:checked,.drawer:not(.drawer-open)>.drawer-toggle:checked)){scrollbar-gutter:stable;background-image:linear-gradient(var(--color-base-100),var(--color-base-100));--root-bg:var(--color-base-100)}@supports (color:color-mix(in lab,red,red)){:where(:root:has(.modal-open,.modal[open],.modal:target,.modal-toggle:checked,.drawer:not(.drawer-open)>.drawer-toggle:checked)){--root-bg:color-mix(in srgb,var(--color-base-100),oklch(0% 0 0) 40%)}}:where(.modal[open],.modal-open,.modal-toggle:checked+.modal):not(.modal-start,.modal-end){scrollbar-gutter:stable}:root{--fx-noise:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E")}.chat{--mask-chat:url("data:image/svg+xml,%3csvg width='13' height='13' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M0 11.5004C0 13.0004 2 13.0004 2 13.0004H12H13V0.00036329L12.5 0C12.5 0 11.977 2.09572 11.8581 2.50033C11.6075 3.35237 10.9149 4.22374 9 5.50036C6 7.50036 0 10.0004 0 11.5004Z'/%3e%3c/svg%3e")}:where(:root),:root:has(input.theme-controller[value=ziydia-light]:checked),[data-theme=ziydia-light]{color-scheme:light;--color-primary:#146ebe;--color-primary-content:#fff;--color-onprimary-container:#003258;--color-neutral:#000;--color-filled:#dddee4;--color-on-filled:#a6a8ae;--color-background-v2:#f8f9ff;--color-surface:#f0f3f8;--color-divider:#d5d8df;--color-secondary:#535f70;--color-secondary-focus:#3a4450;--color-secondary-content:#fff;--color-accent:#1e3050;--color-accent-content:#fff;--color-base-100:#f8f9ff;--color-base-200:#f0f3f8;--color-base-300:#e6e8ee;--color-base-content:#43474e;--color-info:#1e3050;--color-info-content:#fff;--color-success:#4caf50;--color-success-content:#fff;--color-warning:#fedd00;--color-warning-content:#191c20;--color-error:#ba1a1a;--color-error-content:#fff;--color-text-aya:#0a0;--color-text-hash:#464646;--color-text-matn:#0000fa;--color-text-title:maroon;--color-text-number:#be0000;--color-text-base:#000;--rounded-box:.5rem;--rounded-btn:.25rem;--rounded-badge:.125rem;--animation-btn:.25s;--animation-input:.2s;--btn-focus-scale:.95;--border-btn:1px;--tab-border:1px;--tab-radius:.25rem}:root:has(input.theme-controller[value=ziydia-dark]:checked),[data-theme=ziydia-dark]{color-scheme:dark;--color-primary:#a0cafd;--color-primary-focus:#9ecbff;--color-primary-content:#003258;--color-neutral:#fff;--color-primary-container:#146ebe;--color-onprimary-container:#a0cafd;--color-filled:#2a2d31;--color-on-filled:#a6a8ae;--color-netural:#111418;--color-background-v2:#333;--color-surface:#202529;--color-divider:#33363c;--color-secondary:#bbc7db;--color-secondary-focus:#a6b2c6;--color-secondary-content:#000;--color-accent:#fff;--color-accent-content:#003258;--color-base-100:#111418;--color-base-200:#202529;--color-base-300:#272a2f;--color-base-content:#e2e2e6;--color-info:#fff;--color-info-content:#1d2024;--color-success:#86efac;--color-success-content:#052e16;--color-warning:#fedd00;--color-warning-content:#1a1712;--color-error:#ffb4ab;--color-error-content:#690005;--color-text-aya:#00d100;--color-text-hash:#b3b3b3;--color-text-matn:#adadff;--color-text-title:#f99;--color-text-number:#dca7a7;--color-text-base:#000;--rounded-box:.5rem;--rounded-btn:.25rem;--rounded-badge:.125rem;--animation-btn:.25s;--animation-input:.2s;--btn-focus-scale:.95;--border-btn:1px;--tab-border:1px;--tab-radius:.25rem}}@layer components;@layer utilities{.diff{webkit-user-select:none;-webkit-user-select:none;user-select:none;direction:ltr;grid-template-columns:auto 1fr;width:100%;display:grid;position:relative;overflow:hidden;container-type:inline-size}.diff:focus-visible,.diff:has(.diff-item-1:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px;outline-color:var(--color-base-content)}.diff:focus-visible .diff-resizer{min-width:90cqi;max-width:90cqi}.diff:has(.diff-item-2:focus-visible){outline-style:var(--tw-outline-style);outline-offset:1px;outline-width:2px}.diff:has(.diff-item-2:focus-visible) .diff-resizer{min-width:10cqi;max-width:10cqi}@supports (-webkit-overflow-scrolling:touch) and (overflow:-webkit-paged-x){.diff:focus .diff-resizer{min-width:10cqi;max-width:10cqi}.diff:has(.diff-item-1:focus) .diff-resizer{min-width:90cqi;max-width:90cqi}}.modal{pointer-events:none;visibility:hidden;width:100%;max-width:none;height:100%;max-height:none;color:inherit;transition:translate .3s ease-out,visibility .3s allow-discrete,background-color .3s ease-out,opacity .1s ease-out;overscroll-behavior:contain;z-index:999;background-color:#0000;place-items:center;margin:0;padding:0;display:grid;position:fixed;top:0;right:0;bottom:0;left:0;overflow:hidden}.modal::backdrop{display:none}.modal.modal-open,.modal[open],.modal:target{pointer-events:auto;visibility:visible;opacity:1;background-color:#0006}:is(.modal.modal-open,.modal[open],.modal:target) .modal-box{opacity:1;translate:0;scale:1}@starting-style{.modal.modal-open,.modal[open],.modal:target{visibility:hidden;opacity:0}}.drawer-side{pointer-events:none;visibility:hidden;z-index:1;overscroll-behavior:contain;opacity:0;width:100%;transition:opacity .2s ease-out .1s allow-discrete,visibility .3s ease-out .1s allow-discrete;inset-inline-start:0;grid-template-rows:repeat(1,minmax(0,1fr));grid-template-columns:repeat(1,minmax(0,1fr));grid-row-start:1;grid-column-start:1;place-items:flex-start start;height:100dvh;display:grid;position:fixed;top:0;overflow:hidden}.drawer-side>.drawer-overlay{cursor:pointer;background-color:#0006;place-self:stretch stretch;position:sticky;top:0}.drawer-side>*{grid-row-start:1;grid-column-start:1}.drawer-side>:not(.drawer-overlay){will-change:transform;transition:translate .3s ease-out;translate:-100%}[dir=rtl] :is(.drawer-side>:not(.drawer-overlay)){translate:100%}.drawer-toggle{-webkit-appearance:none;-moz-appearance:none;appearance:none;opacity:0;width:0;height:0;position:fixed}.drawer-toggle:checked~.drawer-side{pointer-events:auto;visibility:visible;opacity:1;overflow-y:auto}.drawer-toggle:checked~.drawer-side>:not(.drawer-overlay){translate:0%}.drawer-toggle:focus-visible~.drawer-content label.drawer-button{outline-offset:2px;outline:2px solid}.tooltip{--tt-bg:var(--color-neutral);--tt-off: calc(100% + .5rem) ;--tt-tail: calc(100% + 1px + .25rem) ;display:inline-block;position:relative}.tooltip>:where(.tooltip-content),.tooltip[data-tip]:before{border-radius:var(--radius-field);text-align:center;white-space:normal;max-width:20rem;color:var(--color-neutral-content);opacity:0;background-color:var(--tt-bg);pointer-events:none;z-index:1;--tw-content:attr(data-tip);content:var(--tw-content);width:max-content;padding-block:.25rem;padding-inline:.5rem;font-size:.875rem;line-height:1.25em;transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;position:absolute}.tooltip:after{opacity:0;background-color:var(--tt-bg);content:"";pointer-events:none;--mask-tooltip:url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");width:.625rem;height:.25rem;-webkit-mask-position:-1px 0;mask-position:-1px 0;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-image:var(--mask-tooltip);mask-image:var(--mask-tooltip);transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;display:block;position:absolute}:is(.tooltip.tooltip-open,.tooltip[data-tip]:hover,.tooltip:hover,.tooltip:has(:focus-visible))>.tooltip-content,:is(.tooltip.tooltip-open,.tooltip[data-tip]:hover,.tooltip:hover,.tooltip:has(:focus-visible))[data-tip]:before,:is(.tooltip.tooltip-open,.tooltip[data-tip]:hover,.tooltip:hover,.tooltip:has(:focus-visible)):after{opacity:1;--tt-pos:0rem;transition:opacity .2s cubic-bezier(.4,0,.2,1),transform .2s cubic-bezier(.4,0,.2,1)}.tooltip>.tooltip-content,.tooltip[data-tip]:before{transform:translate(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-off)50%}.tooltip:after{transform:translate(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-tail)50%}.tab{cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;text-align:center;webkit-user-select:none;-webkit-user-select:none;user-select:none;--tab-p:1rem;--tab-bg:var(--color-base-100);--tab-border-color:var(--color-base-300);--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:0;--tab-radius-ee:0;--tab-order:0;--tab-radius-min:calc(.75rem - var(--border));flex-wrap:wrap;order:var(--tab-order);height:calc(var(--size-field,.25rem)*10);border-color:#0000;justify-content:center;align-items:center;padding-inline-start:var(--tab-p);padding-inline-end:var(--tab-p);font-size:.875rem;display:inline-flex;position:relative}@media (hover:hover){.tab:hover{color:var(--color-base-content)}}.tab:is(input[type=radio]){min-width:fit-content}.tab:is(input[type=radio]):after{content:attr(aria-label)}.tab:is(label){position:relative}.tab:is(label) input{cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;opacity:0;position:absolute;top:0;right:0;bottom:0;left:0}:is(.tab:checked,.tab:is(label:has(:checked)),.tab:is(.tab-active,[aria-selected=true]))+.tab-content{height:100%;display:block}.tab:not(:checked,label:has(:checked),:hover,.tab-active,[aria-selected=true]){color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){.tab:not(:checked,label:has(:checked),:hover,.tab-active,[aria-selected=true]){color:color-mix(in oklab,var(--color-base-content)50%,transparent)}}.tab:not(input):empty{cursor:default;flex-grow:1}.tab:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.tab:focus{outline-offset:2px;outline:2px solid #0000}}.tab:focus-visible,.tab:is(label:has(:checked:focus-visible)){outline-offset:-5px;outline:2px solid}.tab[disabled]{pointer-events:none;opacity:.4}.menu{--menu-active-fg:var(--color-neutral-content);--menu-active-bg:var(--color-neutral);flex-flow:column wrap;width:fit-content;padding:.5rem;font-size:.875rem;display:flex}.menu :where(li ul){white-space:nowrap;margin-inline-start:1rem;padding-inline-start:.5rem;position:relative}.menu :where(li ul):before{background-color:var(--color-base-content);opacity:.1;width:var(--border);content:"";inset-inline-start:0;position:absolute;top:.75rem;bottom:.75rem}.menu :where(li>.menu-dropdown:not(.menu-dropdown-show)){display:none}.menu :where(li:not(.menu-title)>:not(ul,details,.menu-title,.btn)),.menu :where(li:not(.menu-title)>details>summary:not(.menu-title)){border-radius:var(--radius-field);text-align:start;text-wrap:balance;-webkit-user-select:none;user-select:none;grid-auto-columns:minmax(auto,max-content) auto max-content;grid-auto-flow:column;align-content:flex-start;align-items:center;gap:.5rem;padding-block:.375rem;padding-inline:.75rem;transition-property:color,background-color,box-shadow;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1);display:grid}.menu :where(li>details>summary){--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.menu :where(li>details>summary){outline-offset:2px;outline:2px solid #0000}}.menu :where(li>details>summary)::-webkit-details-marker{display:none}:is(.menu :where(li>details>summary),.menu :where(li>.menu-dropdown-toggle)):after{content:"";transform-origin:50%;pointer-events:none;justify-self:flex-end;width:.375rem;height:.375rem;transition-property:rotate,translate;transition-duration:.2s;display:block;translate:0 -1px;rotate:-135deg;box-shadow:inset 2px 2px}.menu :where(li>details[open]>summary):after,.menu :where(li>.menu-dropdown-toggle.menu-dropdown-show):after{translate:0 1px;rotate:45deg}.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn).menu-focus,.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn):focus-visible{cursor:pointer;background-color:var(--color-base-content);color:var(--color-base-content);--tw-outline-style:none;outline-style:none}@supports (color:color-mix(in lab,red,red)){:is(.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn).menu-focus,.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn):focus-visible){background-color:color-mix(in oklab,var(--color-base-content)10%,transparent)}}@media (forced-colors:active){:is(.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn).menu-focus,.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn):focus-visible){outline-offset:2px;outline:2px solid #0000}}.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){cursor:pointer;background-color:var(--color-base-content);--tw-outline-style:none;outline-style:none;box-shadow:inset 0 1px #00000003,inset 0 -1px #ffffff03}@supports (color:color-mix(in lab,red,red)){.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){background-color:color-mix(in oklab,var(--color-base-content)10%,transparent)}}@media (forced-colors:active){.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){outline-offset:2px;outline:2px solid #0000}}.menu :where(li:empty){background-color:var(--color-base-content);opacity:.1;height:1px;margin:.5rem 1rem}.menu :where(li){flex-flow:column wrap;flex-shrink:0;align-items:stretch;display:flex;position:relative}.menu :where(li) .badge{justify-self:flex-end}.menu :where(li)>:not(ul,.menu-title,details,.btn):active,.menu :where(li)>:not(ul,.menu-title,details,.btn).menu-active,.menu :where(li)>details>summary:active{--tw-outline-style:none;color:var(--menu-active-fg);background-color:var(--menu-active-bg);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);outline-style:none}@media (forced-colors:active){:is(.menu :where(li)>:not(ul,.menu-title,details,.btn):active,.menu :where(li)>:not(ul,.menu-title,details,.btn).menu-active,.menu :where(li)>details>summary:active){outline-offset:2px;outline:2px solid #0000}}:is(.menu :where(li)>:not(ul,.menu-title,details,.btn):active,.menu :where(li)>:not(ul,.menu-title,details,.btn).menu-active,.menu :where(li)>details>summary:active):not(:is(.menu :where(li)>:not(ul,.menu-title,details,.btn):active,.menu :where(li)>:not(ul,.menu-title,details,.btn).menu-active,.menu :where(li)>details>summary:active):active){box-shadow:0 2px calc(var(--depth)*3px) -2px var(--menu-active-bg)}.menu :where(li).menu-disabled{pointer-events:none;color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){.menu :where(li).menu-disabled{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}.menu .dropdown:focus-within .menu-dropdown-toggle:after{translate:0 1px;rotate:45deg}.menu .dropdown-content{margin-top:.5rem;padding:.5rem}.menu .dropdown-content:before{display:none}.dropdown{position-area:var(--anchor-v,bottom)var(--anchor-h,span-right);display:inline-block;position:relative}.dropdown>:not(summary):focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.dropdown>:not(summary):focus{outline-offset:2px;outline:2px solid #0000}}.dropdown .dropdown-content{position:absolute}.dropdown:not(details,.dropdown-open,.dropdown-hover:hover,:focus-within) .dropdown-content{transform-origin:top;opacity:0;display:none;scale:95%}.dropdown[popover],.dropdown .dropdown-content{z-index:999;transition-behavior:allow-discrete;transition-property:opacity,scale,display;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);animation:.2s dropdown}@starting-style{.dropdown[popover],.dropdown .dropdown-content{opacity:0;scale:95%}}:is(.dropdown.dropdown-open,.dropdown:not(.dropdown-hover):focus,.dropdown:focus-within)>[tabindex]:first-child{pointer-events:none}:is(.dropdown.dropdown-open,.dropdown:not(.dropdown-hover):focus,.dropdown:focus-within) .dropdown-content{opacity:1}.dropdown.dropdown-hover:hover .dropdown-content{opacity:1;scale:100%}.dropdown:is(details) summary::-webkit-details-marker{display:none}:is(.dropdown.dropdown-open,.dropdown:focus,.dropdown:focus-within) .dropdown-content{scale:100%}.dropdown:where([popover]){background:0 0}.dropdown[popover]{color:inherit;position:fixed}@supports not (position-area:bottom){.dropdown[popover]{margin:auto}.dropdown[popover].dropdown-open:not(:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}.dropdown[popover]::backdrop{background-color:oklab(0% none none/.3)}}.dropdown[popover]:not(.dropdown-open,:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}.btn{cursor:pointer;text-align:center;vertical-align:middle;outline-offset:2px;webkit-user-select:none;-webkit-user-select:none;user-select:none;padding-inline:var(--btn-p);color:var(--btn-fg);--tw-prose-links:var(--btn-fg);height:var(--size);font-size:var(--fontsize,.875rem);outline-color:var(--btn-color,var(--color-base-content));background-color:var(--btn-bg);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--btn-noise);border-width:var(--border);border-style:solid;border-color:var(--btn-border);text-shadow:0 .5px oklch(100% 0 0/calc(var(--depth)*.15));box-shadow:0 .5px 0 .5px oklch(100% 0 0/calc(var(--depth)*6%)) inset,var(--btn-shadow);--size:calc(var(--size-field,.25rem)*10);--btn-bg:var(--btn-color,var(--color-base-200));--btn-fg:var(--color-base-content);--btn-p:1rem;--btn-border:var(--btn-bg);--btn-shadow:0 3px 2px -2px var(--btn-bg),0 4px 3px -2px var(--btn-bg);--btn-noise:var(--fx-noise);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-wrap:nowrap;flex-shrink:0;justify-content:center;align-items:center;gap:.375rem;font-weight:600;transition-property:color,background-color,border-color,box-shadow;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1);display:inline-flex}:where(.btn){width:unset}@supports (color:color-mix(in lab,red,red)){.btn{--btn-border:color-mix(in oklab,var(--btn-bg),#000 calc(var(--depth)*5%));--btn-shadow:0 3px 2px -2px color-mix(in oklab,var(--btn-bg)calc(var(--depth)*30%),#0000),0 4px 3px -2px color-mix(in oklab,var(--btn-bg)calc(var(--depth)*30%),#0000)}}.prose .btn{text-decoration-line:none}@media (hover:hover){.btn:hover{--btn-bg:var(--btn-color,var(--color-base-200))}@supports (color:color-mix(in lab,red,red)){.btn:hover{--btn-bg:color-mix(in oklab,var(--btn-color,var(--color-base-200)),#000 7%)}}}.btn:focus-visible{isolation:isolate;outline-width:2px;outline-style:solid}.btn:active:not(.btn-active){--btn-bg:var(--btn-color,var(--color-base-200));--btn-border:var(--btn-color,var(--color-base-200));--btn-shadow:0 0 0 0 oklch(0% 0 0/0),0 0 0 0 oklch(0% 0 0/0);translate:0 .5px}@supports (color:color-mix(in lab,red,red)){.btn:active:not(.btn-active){--btn-bg:color-mix(in oklab,var(--btn-color,var(--color-base-200)),#000 5%);--btn-border:color-mix(in oklab,var(--btn-color,var(--color-base-200)),#000 7%)}}.btn:is(:disabled,[disabled],.btn-disabled){pointer-events:none;--btn-border:#0000;--btn-noise:none;--btn-fg:var(--color-base-content)}.btn:is(:disabled,[disabled],.btn-disabled):not(.btn-link,.btn-ghost){background-color:var(--color-base-content);box-shadow:none}@supports (color:color-mix(in lab,red,red)){.btn:is(:disabled,[disabled],.btn-disabled):not(.btn-link,.btn-ghost){background-color:color-mix(in oklab,var(--color-base-content)10%,transparent)}.btn:is(:disabled,[disabled],.btn-disabled){--btn-fg:color-mix(in oklch,var(--color-base-content)20%,#0000)}}@media (hover:hover){.btn:is(:disabled,[disabled],.btn-disabled):hover{pointer-events:none;background-color:var(--color-neutral);--btn-border:#0000;--btn-fg:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){.btn:is(:disabled,[disabled],.btn-disabled):hover{background-color:color-mix(in oklab,var(--color-neutral)20%,transparent);--btn-fg:color-mix(in oklch,var(--color-base-content)20%,#0000)}}}.btn:is(input[type=checkbox],input[type=radio]){-webkit-appearance:none;-moz-appearance:none;appearance:none}.btn:is(input[type=checkbox],input[type=radio]):after{content:attr(aria-label)}.btn:where(input:checked:not(.filter .btn)){--btn-color:var(--color-primary);--btn-fg:var(--color-primary-content);isolation:isolate}.loading{pointer-events:none;aspect-ratio:1;vertical-align:middle;width:calc(var(--size-selector,.25rem)*6);background-color:currentColor;display:inline-block;-webkit-mask-image:url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");mask-image:url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");-webkit-mask-position:50%;mask-position:50%;-webkit-mask-size:100%;mask-size:100%;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}.pointer-events-auto{pointer-events:auto}.pointer-events-none{pointer-events:none}.collapse{border-radius:var(--radius-box,1rem);isolation:isolate;grid-template-rows:max-content 0fr;width:100%;transition:grid-template-rows .2s;display:grid;position:relative;overflow:hidden}.collapse:not(td,tr,colgroup){visibility:visible}.collapse>input:is([type=checkbox],[type=radio]){-webkit-appearance:none;-moz-appearance:none;appearance:none;opacity:0;z-index:1;grid-row-start:1;grid-column-start:1;width:100%;min-height:3.75rem;padding:1rem;padding-inline-end:3rem;transition:background-color .2s ease-out}.collapse:is([open],:focus:not(.collapse-close)),.collapse:not(.collapse-close):has(>input:is([type=checkbox],[type=radio]):checked){grid-template-rows:max-content 1fr}.collapse:is([open],:focus:not(.collapse-close))>.collapse-content,.collapse:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){visibility:visible;min-height:fit-content}.collapse:focus-visible,.collapse:has(>input:is([type=checkbox],[type=radio]):focus-visible){outline-color:var(--color-base-content);outline-offset:2px;outline-width:2px;outline-style:solid}.collapse:not(.collapse-close)>input[type=checkbox],.collapse:not(.collapse-close)>input[type=radio]:not(:checked),.collapse:not(.collapse-close)>.collapse-title{cursor:pointer}.collapse:focus:not(.collapse-close,.collapse[open])>.collapse-title{cursor:unset}.collapse:is([open],:focus:not(.collapse-close))>:where(.collapse-content),.collapse:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}.collapse[open].collapse-arrow>.collapse-title:after,.collapse.collapse-open.collapse-arrow>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}.collapse.collapse-open.collapse-plus>.collapse-title:after{content:"−"}.collapse.collapse-arrow:focus:not(.collapse-close)>.collapse-title:after,.collapse.collapse-arrow:not(.collapse-close)>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{transform:translateY(-50%)rotate(225deg)}.collapse[open].collapse-plus>.collapse-title:after,.collapse.collapse-plus:focus:not(.collapse-close)>.collapse-title:after,.collapse.collapse-plus:not(.collapse-close)>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{content:"−"}.collapse:is(details){width:100%}.collapse:is(details) summary{display:block;position:relative}.collapse:is(details) summary::-webkit-details-marker{display:none}.collapse:is(details) summary{outline:none}.validator:user-valid{--input-color:var(--color-success)}.validator:user-valid:focus{--input-color:var(--color-success)}.validator:user-valid:checked{--input-color:var(--color-success)}.validator:user-valid[aria-checked=true]{--input-color:var(--color-success)}.validator:user-valid:focus-within{--input-color:var(--color-success)}.validator:has(:user-valid){--input-color:var(--color-success)}.validator:has(:user-valid):focus{--input-color:var(--color-success)}.validator:has(:user-valid):checked{--input-color:var(--color-success)}.validator:has(:user-valid)[aria-checked=true]{--input-color:var(--color-success)}.validator:has(:user-valid):focus-within{--input-color:var(--color-success)}.validator:user-invalid{--input-color:var(--color-error)}.validator:user-invalid:focus{--input-color:var(--color-error)}.validator:user-invalid:checked{--input-color:var(--color-error)}.validator:user-invalid[aria-checked=true]{--input-color:var(--color-error)}.validator:user-invalid:focus-within{--input-color:var(--color-error)}.validator:user-invalid~.validator-hint{visibility:visible;color:var(--color-error);display:block}.validator:has(:user-invalid){--input-color:var(--color-error)}.validator:has(:user-invalid):focus{--input-color:var(--color-error)}.validator:has(:user-invalid):checked{--input-color:var(--color-error)}.validator:has(:user-invalid)[aria-checked=true]{--input-color:var(--color-error)}.validator:has(:user-invalid):focus-within{--input-color:var(--color-error)}.validator:has(:user-invalid)~.validator-hint{visibility:visible;color:var(--color-error);display:block}.validator[aria-invalid],.validator[aria-invalid]:focus,.validator[aria-invalid]:checked,.validator[aria-invalid][aria-checked=true],.validator[aria-invalid]:focus-within{--input-color:var(--color-error)}.validator[aria-invalid]~.validator-hint{visibility:visible;color:var(--color-error);display:block}.validator~.validator-hint{visibility:hidden}.collapse{visibility:collapse}.invisible{visibility:hidden}.visible{visibility:visible}.list{flex-direction:column;font-size:.875rem;display:flex}.list :where(.list-row){--list-grid-cols:minmax(0,auto)1fr;border-radius:var(--radius-box);word-break:break-word;grid-auto-flow:column;grid-template-columns:var(--list-grid-cols);gap:1rem;padding:1rem;display:grid;position:relative}.list :where(.list-row):has(.list-col-grow:first-child){--list-grid-cols:1fr}.list :where(.list-row):has(.list-col-grow:nth-child(2)){--list-grid-cols:minmax(0,auto)1fr}.list :where(.list-row):has(.list-col-grow:nth-child(3)){--list-grid-cols:minmax(0,auto)minmax(0,auto)1fr}.list :where(.list-row):has(.list-col-grow:nth-child(4)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}.list :where(.list-row):has(.list-col-grow:nth-child(5)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}.list :where(.list-row):has(.list-col-grow:nth-child(6)){--list-grid-cols:minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)minmax(0,auto)1fr}.list :where(.list-row) :not(.list-col-wrap){grid-row-start:1}:is(.list>:not(:last-child).list-row,.list>:not(:last-child) .list-row):after{content:"";border-bottom:var(--border)solid;inset-inline:var(--radius-box);border-color:var(--color-base-content);position:absolute;bottom:0}@supports (color:color-mix(in lab,red,red)){:is(.list>:not(:last-child).list-row,.list>:not(:last-child) .list-row):after{border-color:color-mix(in oklab,var(--color-base-content)5%,transparent)}}.toast{translate:var(--toast-x,0)var(--toast-y,0);inset-inline:auto 1rem;background-color:#0000;flex-direction:column;gap:.5rem;width:max-content;max-width:calc(100vw - 2rem);display:flex;position:fixed;top:auto;bottom:1rem}.toast>*{animation:.25s ease-out toast}.toast:where(.toast-start){--toast-x:0;inset-inline:1rem auto}.toast:where(.toast-center){--toast-x:-50%;inset-inline:50%}.toast:where(.toast-end){--toast-x:0;inset-inline:auto 1rem}.toast:where(.toast-bottom){--toast-y:0;top:auto;bottom:1rem}.toast:where(.toast-middle){--toast-y:-50%;top:50%;bottom:auto}.toast:where(.toast-top){--toast-y:0;top:1rem;bottom:auto}.toggle{border:var(--border)solid currentColor;color:var(--input-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;vertical-align:middle;webkit-user-select:none;-webkit-user-select:none;user-select:none;--radius-selector-max:calc(var(--radius-selector) + var(--radius-selector) + var(--radius-selector));border-radius:calc(var(--radius-selector) + min(var(--toggle-p),var(--radius-selector-max)) + min(var(--border),var(--radius-selector-max)));padding:var(--toggle-p);--input-color:var(--color-base-content);--toggle-p:calc(var(--size)*.125);--size:calc(var(--size-selector,.25rem)*6);width:calc((var(--size)*2) - (var(--border) + var(--toggle-p))*2);height:var(--size);flex-shrink:0;grid-template-columns:0fr 1fr 1fr;place-content:center;transition:color .3s,grid-template-columns .2s;display:inline-grid;position:relative;box-shadow:inset 0 1px}@supports (color:color-mix(in lab,red,red)){.toggle{box-shadow:0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000) inset;--input-color:color-mix(in oklab,var(--color-base-content)50%,#0000)}}.toggle>*{z-index:1;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:#0000;border:none;grid-column:2/span 1;grid-row-start:1;height:100%;padding:.125rem;transition:opacity .2s,rotate .4s}.toggle>:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.toggle>:focus{outline-offset:2px;outline:2px solid #0000}}.toggle>:nth-child(2){color:var(--color-base-100);rotate:none}.toggle>:nth-child(3){color:var(--color-base-100);opacity:0;rotate:-15deg}.toggle:has(:checked)>:nth-child(2){opacity:0;rotate:15deg}.toggle:has(:checked)>:nth-child(3){opacity:1;rotate:none}.toggle:before{aspect-ratio:1;border-radius:var(--radius-selector);--tw-content:"";content:var(--tw-content);height:100%;box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px currentColor;background-color:currentColor;background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);grid-row-start:1;grid-column-start:2;transition:background-color .1s,translate .2s,inset-inline-start .2s;position:relative;inset-inline-start:0;translate:0}@supports (color:color-mix(in lab,red,red)){.toggle:before{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000)}}@media (forced-colors:active){.toggle:before{outline-style:var(--tw-outline-style);outline-offset:-1px;outline-width:1px}}@media print{.toggle:before{outline-offset:-1rem;outline:.25rem solid}}.toggle:focus-visible,.toggle:has(:focus-visible){outline-offset:2px;outline:2px solid}.toggle:checked,.toggle[aria-checked=true],.toggle:has(>input:checked){background-color:var(--color-base-100);--input-color:var(--color-base-content);grid-template-columns:1fr 1fr 0fr}:is(.toggle:checked,.toggle[aria-checked=true],.toggle:has(>input:checked)):before{background-color:currentColor}@starting-style{:is(.toggle:checked,.toggle[aria-checked=true],.toggle:has(>input:checked)):before{opacity:0}}.toggle:indeterminate{grid-template-columns:.5fr 1fr .5fr}.toggle:disabled{cursor:not-allowed;opacity:.3}.toggle:disabled:before{border:var(--border)solid currentColor;background-color:#0000}.input{cursor:text;border:var(--border)solid #0000;-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:var(--color-base-100);vertical-align:middle;white-space:nowrap;width:clamp(3rem,20rem,100%);height:var(--size);border-color:var(--input-color);box-shadow:0 1px var(--input-color) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;--size:calc(var(--size-field,.25rem)*10);--input-color:var(--color-base-content);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.5rem;padding-inline:.75rem;font-size:.875rem;display:inline-flex;position:relative}@supports (color:color-mix(in lab,red,red)){.input{box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000)}}.input:where(input){display:inline-flex}.input :where(input){-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:#0000;border:none;width:100%;height:100%;display:inline-flex}.input :where(input):focus,.input :where(input):focus-within{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){:is(.input :where(input):focus,.input :where(input):focus-within){outline-offset:2px;outline:2px solid #0000}}.input :where(input[type=date]){display:inline-block}.input:focus,.input:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px var(--input-color);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}@supports (color:color-mix(in lab,red,red)){:is(.input:focus,.input:focus-within){box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)}}.input:has(>input[disabled]),.input:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:var(--color-base-content);box-shadow:none}@supports (color:color-mix(in lab,red,red)){:is(.input:has(>input[disabled]),.input:is(:disabled,[disabled])){color:color-mix(in oklab,var(--color-base-content)40%,transparent)}}:is(.input:has(>input[disabled]),.input:is(:disabled,[disabled]))::placeholder{color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){:is(.input:has(>input[disabled]),.input:is(:disabled,[disabled]))::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}.input:has(>input[disabled])>input[disabled]{cursor:not-allowed}.input::-webkit-date-and-time-value{text-align:inherit}.input[type=number]::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}.input::-webkit-calendar-picker-indicator{position:absolute;inset-inline-end:.75em}.input\!{cursor:text!important;border:var(--border)solid #0000!important;-webkit-appearance:none!important;-moz-appearance:none!important;appearance:none!important;background-color:var(--color-base-100)!important;vertical-align:middle!important;white-space:nowrap!important;width:clamp(3rem,20rem,100%)!important;height:var(--size)!important;border-color:var(--input-color)!important;box-shadow:0 1px var(--input-color) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset!important;--size:calc(var(--size-field,.25rem)*10)!important;--input-color:var(--color-base-content)!important;border-start-start-radius:var(--join-ss,var(--radius-field))!important;border-start-end-radius:var(--join-se,var(--radius-field))!important;border-end-end-radius:var(--join-ee,var(--radius-field))!important;border-end-start-radius:var(--join-es,var(--radius-field))!important;flex-shrink:1!important;align-items:center!important;gap:.5rem!important;padding-inline:.75rem!important;font-size:.875rem!important;display:inline-flex!important;position:relative!important}@supports (color:color-mix(in lab,red,red)){.input\!{box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset!important;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000)!important}}.input\!:where(input){display:inline-flex!important}.input\! :where(input){-webkit-appearance:none!important;-moz-appearance:none!important;appearance:none!important;background-color:#0000!important;border:none!important;width:100%!important;height:100%!important;display:inline-flex!important}.input\! :where(input):focus,.input\! :where(input):focus-within{--tw-outline-style:none!important;outline-style:none!important}@media (forced-colors:active){:is(.input\! :where(input):focus,.input\! :where(input):focus-within){outline-offset:2px!important;outline:2px solid #0000!important}}.input\! :where(input[type=date]){display:inline-block!important}.input\!:focus,.input\!:focus-within{--input-color:var(--color-base-content)!important;box-shadow:0 1px var(--input-color)!important;outline:2px solid var(--input-color)!important;outline-offset:2px!important;isolation:isolate!important;z-index:1!important}@supports (color:color-mix(in lab,red,red)){:is(.input\!:focus,.input\!:focus-within){box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)!important}}.input\!:has(>input[disabled]),.input\!:is(:disabled,[disabled]){cursor:not-allowed!important;border-color:var(--color-base-200)!important;background-color:var(--color-base-200)!important;color:var(--color-base-content)!important;box-shadow:none!important}@supports (color:color-mix(in lab,red,red)){:is(.input\!:has(>input[disabled]),.input\!:is(:disabled,[disabled])){color:color-mix(in oklab,var(--color-base-content)40%,transparent)!important}}:is(.input\!:has(>input[disabled]),.input\!:is(:disabled,[disabled]))::placeholder{color:var(--color-base-content)!important}@supports (color:color-mix(in lab,red,red)){:is(.input\!:has(>input[disabled]),.input\!:is(:disabled,[disabled]))::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)!important}}.input\!:has(>input[disabled])>input[disabled]{cursor:not-allowed!important}.input\!::-webkit-date-and-time-value{text-align:inherit!important}.input\![type=number]::-webkit-inner-spin-button{margin-block:-.75rem!important;margin-inline-end:-.75rem!important}.input\!::-webkit-calendar-picker-indicator{position:absolute!important;inset-inline-end:.75em!important}.indicator{width:max-content;display:inline-flex;position:relative}.indicator :where(.indicator-item){z-index:1;white-space:nowrap;top:var(--inidicator-t,0);bottom:var(--inidicator-b,auto);left:var(--inidicator-s,auto);right:var(--inidicator-e,0);translate:var(--inidicator-x,50%)var(--indicator-y,-50%);position:absolute}.table{border-radius:var(--radius-box);text-align:left;width:100%;font-size:.875rem;position:relative}.table:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:right}@media (hover:hover){:is(.table tr.row-hover,.table tr.row-hover:nth-child(2n)):hover{background-color:var(--color-base-200)}}.table :where(th,td){vertical-align:middle;padding-block:.75rem;padding-inline:1rem}.table :where(thead,tfoot){white-space:nowrap;color:var(--color-base-content);font-size:.875rem;font-weight:600}@supports (color:color-mix(in lab,red,red)){.table :where(thead,tfoot){color:color-mix(in oklab,var(--color-base-content)60%,transparent)}}.table :where(tfoot){border-top:var(--border)solid var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){.table :where(tfoot){border-top:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}}.table :where(.table-pin-rows thead tr){z-index:1;background-color:var(--color-base-100);position:sticky;top:0}.table :where(.table-pin-rows tfoot tr){z-index:1;background-color:var(--color-base-100);position:sticky;bottom:0}.table :where(.table-pin-cols tr th){background-color:var(--color-base-100);position:sticky;left:0;right:0}.table :where(thead tr,tbody tr:not(:last-child)){border-bottom:var(--border)solid var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){.table :where(thead tr,tbody tr:not(:last-child)){border-bottom:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}}.avatar-offline:before{content:"";z-index:1;background-color:var(--color-base-300);outline:2px solid var(--color-base-100);border-radius:3.40282e38px;width:15%;height:15%;display:block;position:absolute;top:7%;right:7%}.avatar-online:before{content:"";z-index:1;background-color:var(--color-success);outline:2px solid var(--color-base-100);border-radius:3.40282e38px;width:15%;height:15%;display:block;position:absolute;top:7%;right:7%}.steps{counter-reset:step;grid-auto-columns:1fr;grid-auto-flow:column;display:inline-grid;overflow:auto hidden}.steps .step{text-align:center;--step-bg:var(--color-base-300);--step-fg:var(--color-base-content);grid-template-rows:40px 1fr;grid-template-columns:auto;place-items:center;min-width:4rem;display:grid}.steps .step:before{width:100%;height:.5rem;color:var(--step-bg);background-color:var(--step-bg);--tw-content:"";content:var(--tw-content);border:1px solid;grid-row-start:1;grid-column-start:1;margin-inline-start:-100%;top:0}.steps .step>.step-icon,.steps .step:not(:has(.step-icon)):after{content:counter(step);counter-increment:step;z-index:1;color:var(--step-fg);background-color:var(--step-bg);border:1px solid var(--step-bg);border-radius:3.40282e38px;grid-row-start:1;grid-column-start:1;place-self:center;place-items:center;width:2rem;height:2rem;display:grid;position:relative}.steps .step:first-child:before{content:none}.steps .step[data-content]:after{content:attr(data-content)}.steps .step-neutral+.step-neutral:before,.steps .step-neutral:after,.steps .step-neutral>.step-icon{--step-bg:var(--color-neutral);--step-fg:var(--color-neutral-content)}.steps .step-primary+.step-primary:before,.steps .step-primary:after,.steps .step-primary>.step-icon{--step-bg:var(--color-primary);--step-fg:var(--color-primary-content)}.steps .step-secondary+.step-secondary:before,.steps .step-secondary:after,.steps .step-secondary>.step-icon{--step-bg:var(--color-secondary);--step-fg:var(--color-secondary-content)}.steps .step-accent+.step-accent:before,.steps .step-accent:after,.steps .step-accent>.step-icon{--step-bg:var(--color-accent);--step-fg:var(--color-accent-content)}.steps .step-info+.step-info:before,.steps .step-info:after,.steps .step-info>.step-icon{--step-bg:var(--color-info);--step-fg:var(--color-info-content)}.steps .step-success+.step-success:before,.steps .step-success:after,.steps .step-success>.step-icon{--step-bg:var(--color-success);--step-fg:var(--color-success-content)}.steps .step-warning+.step-warning:before,.steps .step-warning:after,.steps .step-warning>.step-icon{--step-bg:var(--color-warning);--step-fg:var(--color-warning-content)}.steps .step-error+.step-error:before,.steps .step-error:after,.steps .step-error>.step-icon{--step-bg:var(--color-error);--step-fg:var(--color-error-content)}.range{-webkit-appearance:none;-moz-appearance:none;appearance:none;webkit-appearance:none;--range-thumb:var(--color-base-100);--range-thumb-size:calc(var(--size-selector,.25rem)*6);--range-progress:currentColor;--range-fill:1;--range-p:.25rem;--range-bg:currentColor;cursor:pointer;vertical-align:middle;--radius-selector-max:calc(var(--radius-selector) + var(--radius-selector) + var(--radius-selector));border-radius:calc(var(--radius-selector) + min(var(--range-p),var(--radius-selector-max)));width:clamp(3rem,20rem,100%);height:var(--range-thumb-size);background-color:#0000;border:none;overflow:hidden}@supports (color:color-mix(in lab,red,red)){.range{--range-bg:color-mix(in oklab,currentColor 10%,#0000)}}[dir=rtl] .range{--range-dir:-1}.range:focus{outline:none}.range:focus-visible{outline-offset:2px;outline:2px solid}.range::-webkit-slider-runnable-track{background-color:var(--range-bg);border-radius:var(--radius-selector);width:100%;height:calc(var(--range-thumb-size)*.5)}@media (forced-colors:active){.range::-webkit-slider-runnable-track{border:1px solid}.range::-moz-range-track{border:1px solid}}.range::-webkit-slider-thumb{box-sizing:border-box;border-radius:calc(var(--radius-selector) + min(var(--range-p),var(--radius-selector-max)));height:var(--range-thumb-size);width:var(--range-thumb-size);border:var(--range-p)solid;-webkit-appearance:none;-moz-appearance:none;appearance:none;webkit-appearance:none;color:var(--range-progress);box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px currentColor,0 0 0 2rem var(--range-thumb) inset,calc((var(--range-dir,1)*-100rem) - (var(--range-dir,1)*var(--range-thumb-size)/2)) 0 0 calc(100rem*var(--range-fill));background-color:currentColor;position:relative;top:50%;transform:translateY(-50%)}@supports (color:color-mix(in lab,red,red)){.range::-webkit-slider-thumb{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000),0 0 0 2rem var(--range-thumb) inset,calc((var(--range-dir,1)*-100rem) - (var(--range-dir,1)*var(--range-thumb-size)/2)) 0 0 calc(100rem*var(--range-fill))}}.range::-moz-range-track{background-color:var(--range-bg);border-radius:var(--radius-selector);width:100%;height:calc(var(--range-thumb-size)*.5)}.range::-moz-range-thumb{box-sizing:border-box;border-radius:calc(var(--radius-selector) + min(var(--range-p),var(--radius-selector-max)));height:var(--range-thumb-size);width:var(--range-thumb-size);border:var(--range-p)solid;color:var(--range-progress);box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px currentColor,0 0 0 2rem var(--range-thumb) inset,calc((var(--range-dir,1)*-100rem) - (var(--range-dir,1)*var(--range-thumb-size)/2)) 0 0 calc(100rem*var(--range-fill));background-color:currentColor;position:relative;top:50%}@supports (color:color-mix(in lab,red,red)){.range::-moz-range-thumb{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000),0 0 0 2rem var(--range-thumb) inset,calc((var(--range-dir,1)*-100rem) - (var(--range-dir,1)*var(--range-thumb-size)/2)) 0 0 calc(100rem*var(--range-fill))}}.range:disabled{cursor:not-allowed;opacity:.3}.range\!{-webkit-appearance:none!important;-moz-appearance:none!important;appearance:none!important;webkit-appearance:none!important;--range-thumb:var(--color-base-100)!important;--range-thumb-size:calc(var(--size-selector,.25rem)*6)!important;--range-progress:currentColor!important;--range-fill:1!important;--range-p:.25rem!important;--range-bg:currentColor!important;cursor:pointer!important;vertical-align:middle!important;--radius-selector-max:calc(var(--radius-selector) + var(--radius-selector) + var(--radius-selector))!important;border-radius:calc(var(--radius-selector) + min(var(--range-p),var(--radius-selector-max)))!important;width:clamp(3rem,20rem,100%)!important;height:var(--range-thumb-size)!important;background-color:#0000!important;border:none!important;overflow:hidden!important}@supports (color:color-mix(in lab,red,red)){.range\!{--range-bg:color-mix(in oklab,currentColor 10%,#0000)!important}}[dir=rtl] .range\!{--range-dir:-1!important}.range\!:focus{outline:none!important}.range\!:focus-visible{outline-offset:2px!important;outline:2px solid!important}.range\!::-webkit-slider-runnable-track{background-color:var(--range-bg)!important;border-radius:var(--radius-selector)!important;width:100%!important;height:calc(var(--range-thumb-size)*.5)!important}@media (forced-colors:active){.range\!::-webkit-slider-runnable-track{border:1px solid!important}.range\!::-moz-range-track{border:1px solid!important}}.range\!::-webkit-slider-thumb{box-sizing:border-box!important;border-radius:calc(var(--radius-selector) + min(var(--range-p),var(--radius-selector-max)))!important;height:var(--range-thumb-size)!important;width:var(--range-thumb-size)!important;border:var(--range-p)solid!important;-webkit-appearance:none!important;-moz-appearance:none!important;appearance:none!important;webkit-appearance:none!important;color:var(--range-progress)!important;box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px currentColor,0 0 0 2rem var(--range-thumb) inset,calc((var(--range-dir,1)*-100rem) - (var(--range-dir,1)*var(--range-thumb-size)/2)) 0 0 calc(100rem*var(--range-fill))!important;background-color:currentColor!important;position:relative!important;top:50%!important;transform:translateY(-50%)!important}@supports (color:color-mix(in lab,red,red)){.range\!::-webkit-slider-thumb{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000),0 0 0 2rem var(--range-thumb) inset,calc((var(--range-dir,1)*-100rem) - (var(--range-dir,1)*var(--range-thumb-size)/2)) 0 0 calc(100rem*var(--range-fill))!important}}.range\!::-moz-range-track{background-color:var(--range-bg)!important;border-radius:var(--radius-selector)!important;width:100%!important;height:calc(var(--range-thumb-size)*.5)!important}.range\!::-moz-range-thumb{box-sizing:border-box!important;border-radius:calc(var(--radius-selector) + min(var(--range-p),var(--radius-selector-max)))!important;height:var(--range-thumb-size)!important;width:var(--range-thumb-size)!important;border:var(--range-p)solid!important;color:var(--range-progress)!important;box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px currentColor,0 0 0 2rem var(--range-thumb) inset,calc((var(--range-dir,1)*-100rem) - (var(--range-dir,1)*var(--range-thumb-size)/2)) 0 0 calc(100rem*var(--range-fill))!important;background-color:currentColor!important;position:relative!important;top:50%!important}@supports (color:color-mix(in lab,red,red)){.range\!::-moz-range-thumb{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000),0 0 0 2rem var(--range-thumb) inset,calc((var(--range-dir,1)*-100rem) - (var(--range-dir,1)*var(--range-thumb-size)/2)) 0 0 calc(100rem*var(--range-fill))!important}}.range\!:disabled{cursor:not-allowed!important;opacity:.3!important}.countdown{display:inline-flex}.countdown.countdown{line-height:1em}.countdown>*{height:1em;display:inline-block;overflow-y:hidden}.countdown>:before{content:"00\a 01\a 02\a 03\a 04\a 05\a 06\a 07\a 08\a 09\a 10\a 11\a 12\a 13\a 14\a 15\a 16\a 17\a 18\a 19\a 20\a 21\a 22\a 23\a 24\a 25\a 26\a 27\a 28\a 29\a 30\a 31\a 32\a 33\a 34\a 35\a 36\a 37\a 38\a 39\a 40\a 41\a 42\a 43\a 44\a 45\a 46\a 47\a 48\a 49\a 50\a 51\a 52\a 53\a 54\a 55\a 56\a 57\a 58\a 59\a 60\a 61\a 62\a 63\a 64\a 65\a 66\a 67\a 68\a 69\a 70\a 71\a 72\a 73\a 74\a 75\a 76\a 77\a 78\a 79\a 80\a 81\a 82\a 83\a 84\a 85\a 86\a 87\a 88\a 89\a 90\a 91\a 92\a 93\a 94\a 95\a 96\a 97\a 98\a 99\a";white-space:pre;top:calc(var(--value)*-1em);text-align:center;transition:all 1s cubic-bezier(1,0,0,1);position:relative}.select{border:var(--border)solid #0000;-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:var(--color-base-100);vertical-align:middle;width:clamp(3rem,20rem,100%);height:var(--size);text-overflow:ellipsis;box-shadow:0 1px var(--input-color) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;border-color:var(--input-color);--input-color:var(--color-base-content);--size:calc(var(--size-field,.25rem)*10);background-image:linear-gradient(45deg,#0000 50%,currentColor 50%),linear-gradient(135deg,currentColor 50%,#0000 50%);background-position:calc(100% - 20px) calc(1px + 50%),calc(100% - 16.1px) calc(1px + 50%);background-repeat:no-repeat;background-size:4px 4px,4px 4px;border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.375rem;padding-inline:1rem 1.75rem;font-size:.875rem;display:inline-flex;position:relative}@supports (color:color-mix(in lab,red,red)){.select{box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000)}}[dir=rtl] .select{background-position:12px calc(1px + 50%),16px calc(1px + 50%)}.select select{-webkit-appearance:none;-moz-appearance:none;appearance:none;background:inherit;border-radius:inherit;border-style:none;width:calc(100% + 2.75rem);height:calc(100% - 2px);margin-inline:-1rem -1.75rem;padding-inline:1rem 1.75rem}.select select:focus,.select select:focus-within{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){:is(.select select:focus,.select select:focus-within){outline-offset:2px;outline:2px solid #0000}}.select select:not(:last-child){background-image:none;margin-inline-end:-1.375rem}.select:focus,.select:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px var(--input-color);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}@supports (color:color-mix(in lab,red,red)){:is(.select:focus,.select:focus-within){box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)}}.select:has(>select[disabled]),.select:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){:is(.select:has(>select[disabled]),.select:is(:disabled,[disabled])){color:color-mix(in oklab,var(--color-base-content)40%,transparent)}}:is(.select:has(>select[disabled]),.select:is(:disabled,[disabled]))::placeholder{color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){:is(.select:has(>select[disabled]),.select:is(:disabled,[disabled]))::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}.select:has(>select[disabled])>select[disabled]{cursor:not-allowed}.select\!{border:var(--border)solid #0000!important;-webkit-appearance:none!important;-moz-appearance:none!important;appearance:none!important;background-color:var(--color-base-100)!important;vertical-align:middle!important;width:clamp(3rem,20rem,100%)!important;height:var(--size)!important;text-overflow:ellipsis!important;box-shadow:0 1px var(--input-color) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset!important;border-color:var(--input-color)!important;--input-color:var(--color-base-content)!important;--size:calc(var(--size-field,.25rem)*10)!important;background-image:linear-gradient(45deg,#0000 50%,currentColor 50%),linear-gradient(135deg,currentColor 50%,#0000 50%)!important;background-position:calc(100% - 20px) calc(1px + 50%),calc(100% - 16.1px) calc(1px + 50%)!important;background-repeat:no-repeat!important;background-size:4px 4px,4px 4px!important;border-start-start-radius:var(--join-ss,var(--radius-field))!important;border-start-end-radius:var(--join-se,var(--radius-field))!important;border-end-end-radius:var(--join-ee,var(--radius-field))!important;border-end-start-radius:var(--join-es,var(--radius-field))!important;flex-shrink:1!important;align-items:center!important;gap:.375rem!important;padding-inline:1rem 1.75rem!important;font-size:.875rem!important;display:inline-flex!important;position:relative!important}@supports (color:color-mix(in lab,red,red)){.select\!{box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset!important;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000)!important}}[dir=rtl] .select\!{background-position:12px calc(1px + 50%),16px calc(1px + 50%)!important}.select\! select{-webkit-appearance:none!important;-moz-appearance:none!important;appearance:none!important;background:inherit!important;border-radius:inherit!important;border-style:none!important;width:calc(100% + 2.75rem)!important;height:calc(100% - 2px)!important;margin-inline:-1rem -1.75rem!important;padding-inline:1rem 1.75rem!important}.select\! select:focus,.select\! select:focus-within{--tw-outline-style:none!important;outline-style:none!important}@media (forced-colors:active){:is(.select\! select:focus,.select\! select:focus-within){outline-offset:2px!important;outline:2px solid #0000!important}}.select\! select:not(:last-child){background-image:none!important;margin-inline-end:-1.375rem!important}.select\!:focus,.select\!:focus-within{--input-color:var(--color-base-content)!important;box-shadow:0 1px var(--input-color)!important;outline:2px solid var(--input-color)!important;outline-offset:2px!important;isolation:isolate!important;z-index:1!important}@supports (color:color-mix(in lab,red,red)){:is(.select\!:focus,.select\!:focus-within){box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)!important}}.select\!:has(>select[disabled]),.select\!:is(:disabled,[disabled]){cursor:not-allowed!important;border-color:var(--color-base-200)!important;background-color:var(--color-base-200)!important;color:var(--color-base-content)!important}@supports (color:color-mix(in lab,red,red)){:is(.select\!:has(>select[disabled]),.select\!:is(:disabled,[disabled])){color:color-mix(in oklab,var(--color-base-content)40%,transparent)!important}}:is(.select\!:has(>select[disabled]),.select\!:is(:disabled,[disabled]))::placeholder{color:var(--color-base-content)!important}@supports (color:color-mix(in lab,red,red)){:is(.select\!:has(>select[disabled]),.select\!:is(:disabled,[disabled]))::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)!important}}.select\!:has(>select[disabled])>select[disabled]{cursor:not-allowed!important}.timeline{display:flex;position:relative}.timeline>li{grid-template-rows:var(--timeline-row-start,minmax(0,1fr))auto var(--timeline-row-end,minmax(0,1fr));grid-template-columns:var(--timeline-col-start,minmax(0,1fr))auto var(--timeline-col-end,minmax(0,1fr));flex-shrink:0;align-items:center;display:grid;position:relative}.timeline>li>hr{border:none;width:100%}.timeline>li>hr:first-child{grid-row-start:2;grid-column-start:1}.timeline>li>hr:last-child{grid-area:2/3/auto/none}@media print{.timeline>li>hr{border:.1px solid var(--color-base-300)}}.timeline :where(hr){background-color:var(--color-base-300);height:.25rem}.timeline:has(.timeline-middle hr):first-child{border-start-start-radius:0;border-start-end-radius:var(--radius-selector);border-end-end-radius:var(--radius-selector);border-end-start-radius:0}.timeline:has(.timeline-middle hr):last-child,.timeline:not(:has(.timeline-middle)) :first-child hr:last-child{border-start-start-radius:var(--radius-selector);border-start-end-radius:0;border-end-end-radius:0;border-end-start-radius:var(--radius-selector)}.timeline:not(:has(.timeline-middle)) :last-child hr:first-child{border-start-start-radius:0;border-start-end-radius:var(--radius-selector);border-end-end-radius:var(--radius-selector);border-end-start-radius:0}.card{border-radius:var(--radius-box);outline-offset:2px;outline:0 solid #0000;flex-direction:column;transition:outline .2s ease-in-out;display:flex;position:relative}.card:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.card:focus{outline-offset:2px;outline:2px solid #0000}}.card:focus-visible{outline-color:currentColor}.card :where(figure:first-child){border-start-start-radius:inherit;border-start-end-radius:inherit;border-end-end-radius:unset;border-end-start-radius:unset;overflow:hidden}.card :where(figure:last-child){border-start-start-radius:unset;border-start-end-radius:unset;border-end-end-radius:inherit;border-end-start-radius:inherit;overflow:hidden}.card:where(.card-border){border:var(--border)solid var(--color-base-200)}.card:where(.card-dash){border:var(--border)dashed var(--color-base-200)}.card.image-full{display:grid}.card.image-full>*{grid-row-start:1;grid-column-start:1}.card.image-full>.card-body{color:var(--color-neutral-content);position:relative}.card.image-full :where(figure){border-radius:inherit;overflow:hidden}.card.image-full>figure img{object-fit:cover;filter:brightness(28%);height:100%}.card figure{justify-content:center;align-items:center;display:flex}.card:has(>input:is(input[type=checkbox],input[type=radio])){cursor:pointer;-webkit-user-select:none;user-select:none}.card:has(>:checked){outline:2px solid}.swap{cursor:pointer;vertical-align:middle;webkit-user-select:none;-webkit-user-select:none;user-select:none;place-content:center;display:inline-grid;position:relative}.swap input{-webkit-appearance:none;-moz-appearance:none;appearance:none;border:none}.swap>*{grid-row-start:1;grid-column-start:1;transition-property:transform,rotate,opacity;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1)}.swap .swap-on,.swap .swap-indeterminate,.swap input:indeterminate~.swap-on,.swap input:is(:checked,:indeterminate)~.swap-off{opacity:0}.swap input:checked~.swap-on,.swap input:indeterminate~.swap-indeterminate{opacity:1}.swap\!{cursor:pointer!important;vertical-align:middle!important;webkit-user-select:none!important;-webkit-user-select:none!important;user-select:none!important;place-content:center!important;display:inline-grid!important;position:relative!important}.swap\! input{-webkit-appearance:none!important;-moz-appearance:none!important;appearance:none!important;border:none!important}.swap\!>*{grid-row-start:1!important;grid-column-start:1!important;transition-property:transform,rotate,opacity!important;transition-duration:.2s!important;transition-timing-function:cubic-bezier(0,0,.2,1)!important}.swap\! .swap-on,.swap\! .swap-indeterminate,.swap\! input:indeterminate~.swap-on,.swap\! input:is(:checked,:indeterminate)~.swap-off{opacity:0!important}.swap\! input:checked~.swap-on,.swap\! input:indeterminate~.swap-indeterminate{opacity:1!important}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.menu-horizontal{flex-direction:row;display:inline-flex}.menu-horizontal>li:not(.menu-title)>details>ul{margin-inline-start:0;margin-top:1rem;padding-block:.5rem;padding-inline-end:.5rem;position:absolute}.menu-horizontal>li>details>ul:before{content:none}:where(.menu-horizontal>li:not(.menu-title)>details>ul){border-radius:var(--radius-box);background-color:var(--color-base-100);box-shadow:0 1px 3px #0000001a,0 1px 2px -1px #0000001a}.menu-vertical{flex-direction:column;display:inline-flex}.menu-vertical>li:not(.menu-title)>details>ul{margin-inline-start:1rem;margin-top:0;padding-block:0;padding-inline-end:0;position:relative}.avatar{vertical-align:middle;display:inline-flex;position:relative}.avatar>div{aspect-ratio:1;display:block;overflow:hidden}.avatar img{object-fit:cover;width:100%;height:100%}.checkbox{border:var(--border)solid var(--input-color,var(--color-base-content));cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;border-radius:var(--radius-selector);vertical-align:middle;color:var(--color-base-content);box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 0 #0000 inset,0 0 #0000;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);flex-shrink:0;padding:.25rem;transition:background-color .2s,box-shadow .2s;position:relative}@supports (color:color-mix(in lab,red,red)){.checkbox{border:var(--border)solid var(--input-color,color-mix(in oklab,var(--color-base-content)20%,#0000))}}.checkbox:before{--tw-content:"";content:var(--tw-content);opacity:0;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,70% 80%,70% 100%);width:100%;height:100%;box-shadow:0 3px oklch(100% 0 0/calc(var(--depth)*.1)) inset;background-color:currentColor;font-size:1rem;line-height:.75;transition:clip-path .3s .1s,opacity .1s .1s,rotate .3s .1s,translate .3s .1s;display:block;rotate:45deg}.checkbox:focus-visible{outline:2px solid var(--input-color,currentColor);outline-offset:2px}.checkbox:checked,.checkbox[aria-checked=true]{background-color:var(--input-color,#0000);box-shadow:0 0 #0000 inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1))}:is(.checkbox:checked,.checkbox[aria-checked=true]):before{clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 0%,70% 0%,70% 100%);opacity:1}@media (forced-colors:active){:is(.checkbox:checked,.checkbox[aria-checked=true]):before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}@media print{:is(.checkbox:checked,.checkbox[aria-checked=true]):before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}.checkbox:indeterminate:before{opacity:1;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,80% 80%,80% 100%);translate:0 -35%;rotate:none}.checkbox:disabled{cursor:not-allowed;opacity:.2}.radio{cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;vertical-align:middle;border:var(--border)solid var(--input-color,currentColor);box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1)) inset;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);color:var(--input-color,currentColor);border-radius:3.40282e38px;flex-shrink:0;padding:.25rem;position:relative}@supports (color:color-mix(in lab,red,red)){.radio{border:var(--border)solid var(--input-color,color-mix(in srgb,currentColor 20%,#0000))}}.radio:before{--tw-content:"";content:var(--tw-content);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);border-radius:3.40282e38px;width:100%;height:100%;display:block}.radio:focus-visible{outline:2px solid}.radio:checked,.radio[aria-checked=true]{background-color:var(--color-base-100);border-color:currentColor;animation:.2s ease-out radio}:is(.radio:checked,.radio[aria-checked=true]):before{box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));background-color:currentColor}@media (forced-colors:active){:is(.radio:checked,.radio[aria-checked=true]):before{outline-style:var(--tw-outline-style);outline-offset:-1px;outline-width:1px}}@media print{:is(.radio:checked,.radio[aria-checked=true]):before{outline-offset:-1rem;outline:.25rem solid}}.radio:disabled{cursor:not-allowed;opacity:.2}.rating{vertical-align:middle;display:inline-flex;position:relative}.rating input{-webkit-appearance:none;-moz-appearance:none;appearance:none;border:none}.rating :where(*){background-color:var(--color-base-content);opacity:.2;border-radius:0;width:1.5rem;height:1.5rem;animation:.25s ease-out rating}.rating :where(*):is(input){cursor:pointer}.rating .rating-hidden{background-color:#0000;width:.5rem}.rating input[type=radio]:checked{background-image:none}.rating :checked,.rating [aria-checked=true],.rating [aria-current=true],.rating :has(~:checked,~[aria-checked=true],~[aria-current=true]){opacity:1}.rating :focus-visible{transition:scale .2s ease-out;scale:1.1}.rating :active:focus{animation:none;scale:1.1}.rating.rating-xs :where(:not(.rating-hidden)){width:1rem;height:1rem}.rating.rating-sm :where(:not(.rating-hidden)){width:1.25rem;height:1.25rem}.rating.rating-md :where(:not(.rating-hidden)){width:1.5rem;height:1.5rem}.rating.rating-lg :where(:not(.rating-hidden)){width:1.75rem;height:1.75rem}.rating.rating-xl :where(:not(.rating-hidden)){width:2rem;height:2rem}.drawer{grid-auto-columns:max-content auto;width:100%;display:grid;position:relative}.stats{border-radius:var(--radius-box);grid-auto-flow:column;display:inline-grid;position:relative;overflow-x:auto}.progress{-webkit-appearance:none;-moz-appearance:none;appearance:none;border-radius:var(--radius-box);width:100%;height:.5rem;color:var(--color-base-content);background-color:currentColor;position:relative;overflow:hidden}@supports (color:color-mix(in lab,red,red)){.progress{background-color:color-mix(in oklab,currentColor 20%,transparent)}}.progress:indeterminate{background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress}@supports ((-moz-appearance:none)){.progress:indeterminate::-moz-progress-bar{background-color:#0000;background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress}.progress::-moz-progress-bar{border-radius:var(--radius-box);background-color:currentColor}}@supports ((-webkit-appearance:none)){.progress::-webkit-progress-bar{border-radius:var(--radius-box);background-color:#0000}.progress::-webkit-progress-value{border-radius:var(--radius-box);background-color:currentColor}}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.sticky{position:sticky}.inset-0{inset:calc(var(--spacing)*0)}.inset-y-0{inset-block:calc(var(--spacing)*0)}.start-0{inset-inline-start:calc(var(--spacing)*0)}.dropdown-center{--anchor-h:center}.dropdown-center :where(.dropdown-content){inset-inline-end:50%;translate:50%}[dir=rtl] :is(.dropdown-center :where(.dropdown-content)){translate:-50%}.dropdown-center.dropdown-left{--anchor-h:left;--anchor-v:center}.dropdown-center.dropdown-left .dropdown-content{top:auto;bottom:50%;translate:0 50%}.dropdown-center.dropdown-right{--anchor-h:right;--anchor-v:center}.dropdown-center.dropdown-right .dropdown-content{top:auto;bottom:50%;translate:0 50%}.dropdown-end{--anchor-h:span-left}.dropdown-end :where(.dropdown-content){inset-inline-end:0;translate:0}.dropdown-end.dropdown-left{--anchor-h:left;--anchor-v:span-top}.dropdown-end.dropdown-left .dropdown-content{top:auto;bottom:0}.dropdown-end.dropdown-right{--anchor-h:right;--anchor-v:span-top}.dropdown-end.dropdown-right .dropdown-content{top:auto;bottom:0}.end-0{inset-inline-end:calc(var(--spacing)*0)}.dropdown-top{--anchor-v:top}.dropdown-top .dropdown-content{transform-origin:bottom;top:auto;bottom:100%}.top-0{top:calc(var(--spacing)*0)}.top-1{top:calc(var(--spacing)*1)}.top-1\/2{top:50%}.top-2{top:calc(var(--spacing)*2)}.top-4{top:calc(var(--spacing)*4)}.top-9{top:calc(var(--spacing)*9)}.top-50{top:calc(var(--spacing)*50)}.right-0{right:calc(var(--spacing)*0)}.right-1{right:calc(var(--spacing)*1)}.right-2{right:calc(var(--spacing)*2)}.right-4{right:calc(var(--spacing)*4)}.right-7{right:calc(var(--spacing)*7)}.right-8{right:calc(var(--spacing)*8)}.right-\[200\%\]{right:200%}.bottom-0{bottom:calc(var(--spacing)*0)}.bottom-4{bottom:calc(var(--spacing)*4)}.bottom-8{bottom:calc(var(--spacing)*8)}.bottom-10{bottom:calc(var(--spacing)*10)}.-left-\[200\%\]{left:-200%}.left-0{left:calc(var(--spacing)*0)}.left-1\/2{left:50%}.left-2{left:calc(var(--spacing)*2)}.left-3{left:calc(var(--spacing)*3)}.left-4{left:calc(var(--spacing)*4)}.file-input{cursor:pointer;border:var(--border)solid #0000;-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:var(--color-base-100);vertical-align:middle;webkit-user-select:none;-webkit-user-select:none;user-select:none;width:clamp(3rem,20rem,100%);height:var(--size);border-color:var(--input-color);box-shadow:0 1px var(--input-color) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;--size:calc(var(--size-field,.25rem)*10);--input-color:var(--color-base-content);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));align-items:center;padding-inline-end:.75rem;font-size:.875rem;line-height:2;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.file-input{box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000)}}.file-input::file-selector-button{cursor:pointer;webkit-user-select:none;-webkit-user-select:none;user-select:none;height:calc(100% + var(--border)*2);margin-inline-end:1rem;margin-block:calc(var(--border)*-1);color:var(--btn-fg);border-width:var(--border);border-style:solid;border-color:var(--btn-border);background-color:var(--btn-bg);background-size:calc(var(--noise)*100%);background-image:var(--btn-noise);text-shadow:0 .5px oklch(1 0 0/calc(var(--depth)*.15));box-shadow:0 .5px 0 .5px #fff inset,var(--btn-shadow);--size:calc(var(--size-field,.25rem)*10);--btn-bg:var(--btn-color,var(--color-base-200));--btn-fg:var(--color-base-content);--btn-border:var(--btn-bg);--btn-shadow:0 3px 2px -2px var(--btn-bg),0 4px 3px -2px var(--btn-bg);--btn-noise:var(--fx-noise);border-start-start-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-start-radius:calc(var(--join-es,var(--radius-field) - var(--border)));margin-inline-start:calc(var(--border)*-1);padding-inline:1rem;font-size:.875rem;font-weight:600}@supports (color:color-mix(in lab,red,red)){.file-input::file-selector-button{box-shadow:0 .5px 0 .5px color-mix(in oklab,color-mix(in oklab,white 30%,var(--btn-bg))calc(var(--depth)*20%),#0000) inset,var(--btn-shadow);--btn-border:color-mix(in oklab,var(--btn-bg),#000 5%);--btn-shadow:0 3px 2px -2px color-mix(in oklab,var(--btn-bg)30%,#0000),0 4px 3px -2px color-mix(in oklab,var(--btn-bg)30%,#0000)}}.file-input:focus{--input-color:var(--color-base-content);box-shadow:0 1px var(--input-color);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}@supports (color:color-mix(in lab,red,red)){.file-input:focus{box-shadow:0 1px color-mix(in oklab,var(--input-color)10%,#0000)}}.file-input:has(>input[disabled]),.file-input:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);box-shadow:none;color:var(--color-base-content)}:is(.file-input:has(>input[disabled]),.file-input:is(:disabled,[disabled]))::placeholder{color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){:is(.file-input:has(>input[disabled]),.file-input:is(:disabled,[disabled]))::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}:is(.file-input:has(>input[disabled]),.file-input:is(:disabled,[disabled])){color:color-mix(in oklch,var(--color-base-content)20%,#0000)}}:is(.file-input:has(>input[disabled]),.file-input:is(:disabled,[disabled]))::file-selector-button{cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);--btn-border:#0000;--btn-noise:none;--btn-fg:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){:is(.file-input:has(>input[disabled]),.file-input:is(:disabled,[disabled]))::file-selector-button{--btn-fg:color-mix(in oklch,var(--color-base-content)20%,#0000)}}.textarea{border:var(--border)solid #0000;-webkit-appearance:none;-moz-appearance:none;appearance:none;border-radius:var(--radius-field);background-color:var(--color-base-100);vertical-align:middle;border-color:var(--input-color);width:clamp(3rem,20rem,100%);min-height:5rem;box-shadow:0 1px var(--input-color) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;--input-color:var(--color-base-content);flex-shrink:1;padding-block:.5rem;padding-inline:.75rem;font-size:.875rem}@supports (color:color-mix(in lab,red,red)){.textarea{box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000)}}.textarea textarea{-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:#0000;border:none}.textarea textarea:focus,.textarea textarea:focus-within{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){:is(.textarea textarea:focus,.textarea textarea:focus-within){outline-offset:2px;outline:2px solid #0000}}.textarea:focus,.textarea:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px var(--input-color);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}@supports (color:color-mix(in lab,red,red)){:is(.textarea:focus,.textarea:focus-within){box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)}}.textarea:has(>textarea[disabled]),.textarea:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:var(--color-base-content);box-shadow:none}@supports (color:color-mix(in lab,red,red)){:is(.textarea:has(>textarea[disabled]),.textarea:is(:disabled,[disabled])){color:color-mix(in oklab,var(--color-base-content)40%,transparent)}}:is(.textarea:has(>textarea[disabled]),.textarea:is(:disabled,[disabled]))::placeholder{color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){:is(.textarea:has(>textarea[disabled]),.textarea:is(:disabled,[disabled]))::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}.textarea:has(>textarea[disabled])>textarea[disabled]{cursor:not-allowed}.btn-active{--btn-bg:var(--btn-color,var(--color-base-200));--btn-shadow:0 0 0 0 oklch(0% 0 0/0),0 0 0 0 oklch(0% 0 0/0);isolation:isolate}@supports (color:color-mix(in lab,red,red)){.btn-active{--btn-bg:color-mix(in oklab,var(--btn-color,var(--color-base-200)),#000 7%)}}.isolate{isolation:isolate}.stack{grid-template-rows:3px 4px 1fr 4px 3px;grid-template-columns:3px 4px 1fr 4px 3px;display:inline-grid}.stack>*{width:100%;height:100%}.stack>:nth-child(n+2){opacity:.7;width:100%}.stack>:nth-child(2){z-index:2;opacity:.9}.stack>:first-child{z-index:3;width:100%}:is(.stack,.stack.stack-bottom)>*{grid-area:3/3/6/4}:is(.stack,.stack.stack-bottom)>:nth-child(2){grid-area:2/2/5/5}:is(.stack,.stack.stack-bottom)>:first-child{grid-area:1/1/4/6}.stack.stack-top>*{grid-area:1/3/4/4}.stack.stack-top>:nth-child(2){grid-area:2/2/5/5}.stack.stack-top>:first-child{grid-area:3/1/6/6}.stack.stack-start>*{grid-area:3/1/4/4}.stack.stack-start>:nth-child(2){grid-area:2/2/5/5}.stack.stack-start>:first-child{grid-area:1/3/6/6}.stack.stack-end>*{grid-area:3/3/4/6}.stack.stack-end>:nth-child(2){grid-area:2/2/5/5}.stack.stack-end>:first-child{grid-area:1/1/6/4}.stack\!{grid-template-rows:3px 4px 1fr 4px 3px!important;grid-template-columns:3px 4px 1fr 4px 3px!important;display:inline-grid!important}.stack\!>*{width:100%!important;height:100%!important}.stack\!>:nth-child(n+2){opacity:.7!important;width:100%!important}.stack\!>:nth-child(2){z-index:2!important;opacity:.9!important}.stack\!>:first-child{z-index:3!important;width:100%!important}:is(.stack\!,.stack\!.stack-bottom)>*{grid-area:3/3/6/4!important}:is(.stack\!,.stack\!.stack-bottom)>:nth-child(2){grid-area:2/2/5/5!important}:is(.stack\!,.stack\!.stack-bottom)>:first-child{grid-area:1/1/4/6!important}.stack\!.stack-top>*{grid-area:1/3/4/4!important}.stack\!.stack-top>:nth-child(2){grid-area:2/2/5/5!important}.stack\!.stack-top>:first-child{grid-area:3/1/6/6!important}.stack\!.stack-start>*{grid-area:3/1/4/4!important}.stack\!.stack-start>:nth-child(2){grid-area:2/2/5/5!important}.stack\!.stack-start>:first-child{grid-area:1/3/6/6!important}.stack\!.stack-end>*{grid-area:3/3/4/6!important}.stack\!.stack-end>:nth-child(2){grid-area:2/2/5/5!important}.stack\!.stack-end>:first-child{grid-area:1/1/6/4!important}.modal-backdrop{color:#0000;z-index:-1;grid-row-start:1;grid-column-start:1;place-self:stretch stretch;display:grid}.modal-backdrop button{cursor:pointer}.-z-1{z-index:-1}.-z-2{z-index:-2}.z-1{z-index:1}.z-2{z-index:2}.z-10{z-index:10}.z-20{z-index:20}.z-40{z-index:40}.z-50{z-index:50}.z-\[1\]{z-index:1}.tab-content{order:var(--tabcontent-order);--tabcontent-radius-ss:0;--tabcontent-radius-se:0;--tabcontent-radius-es:0;--tabcontent-radius-ee:0;--tabcontent-order:1;width:100%;margin:var(--tabcontent-margin);border-color:#0000;border-width:var(--border);border-start-start-radius:var(--tabcontent-radius-ss);border-start-end-radius:var(--tabcontent-radius-se);border-end-end-radius:var(--tabcontent-radius-ee);border-end-start-radius:var(--tabcontent-radius-es);display:none}.order-1{order:1}.order-2{order:2}.order-3{order:3}.col-3{grid-column:3}.col-4{grid-column:4}.col-5{grid-column:5}.col-6{grid-column:6}.col-7{grid-column:7}.col-8{grid-column:8}.col-9{grid-column:9}.col-12{grid-column:12}.col-span-1{grid-column:span 1/span 1}.col-span-2{grid-column:span 2/span 2}.col-span-3{grid-column:span 3/span 3}.col-span-full{grid-column:1/-1}.hero{background-position:50%;background-size:cover;place-items:center;width:100%;display:grid}.hero>*{grid-row-start:1;grid-column-start:1}.modal-box{background-color:var(--color-base-100);border-top-left-radius:var(--modal-tl,var(--radius-box));border-top-right-radius:var(--modal-tr,var(--radius-box));border-bottom-left-radius:var(--modal-bl,var(--radius-box));border-bottom-right-radius:var(--modal-br,var(--radius-box));opacity:0;overscroll-behavior:contain;grid-row-start:1;grid-column-start:1;width:91.6667%;max-width:32rem;max-height:100vh;padding:1.5rem;transition:translate .3s ease-out,scale .3s ease-out,opacity .2s ease-out 50ms,box-shadow .3s ease-out;overflow-y:auto;scale:95%;box-shadow:0 25px 50px -12px #00000040}.drawer-end{grid-auto-columns:auto max-content}.drawer-end>.drawer-toggle~.drawer-content{grid-column-start:1}.drawer-end>.drawer-toggle~.drawer-side{grid-column-start:2;justify-items:end}.drawer-end>.drawer-toggle~.drawer-side>:not(.drawer-overlay){translate:100%}[dir=rtl] :is(.drawer-end>.drawer-toggle~.drawer-side>:not(.drawer-overlay)){translate:-100%}.drawer-end>.drawer-toggle:checked~.drawer-side>:not(.drawer-overlay){translate:0%}.container{width:100%}@media (min-width:40rem){.container{max-width:40rem}}@media (min-width:48rem){.container{max-width:48rem}}@media (min-width:64rem){.container{max-width:64rem}}@media (min-width:80rem){.container{max-width:80rem}}@media (min-width:96rem){.container{max-width:96rem}}.divider{white-space:nowrap;height:1rem;margin:var(--divider-m,1rem 0);--divider-color:var(--color-base-content);flex-direction:row;align-self:stretch;align-items:center;display:flex}@supports (color:color-mix(in lab,red,red)){.divider{--divider-color:color-mix(in oklab,var(--color-base-content)10%,transparent)}}.divider:before,.divider:after{content:"";background-color:var(--divider-color);flex-grow:1;width:100%;height:.125rem}@media print{.divider:before,.divider:after{border:.5px solid}}.divider:not(:empty){gap:1rem}.m-0{margin:calc(var(--spacing)*0)}.m-1{margin:calc(var(--spacing)*1)}.m-2{margin:calc(var(--spacing)*2)}.m-3{margin:calc(var(--spacing)*3)}.m-4{margin:calc(var(--spacing)*4)}.m-5{margin:calc(var(--spacing)*5)}.m-6{margin:calc(var(--spacing)*6)}.m-7{margin:calc(var(--spacing)*7)}.m-8{margin:calc(var(--spacing)*8)}.m-9{margin:calc(var(--spacing)*9)}.m-10{margin:calc(var(--spacing)*10)}.m-11{margin:calc(var(--spacing)*11)}.m-12{margin:calc(var(--spacing)*12)}.m-13{margin:calc(var(--spacing)*13)}.m-14{margin:calc(var(--spacing)*14)}.m-15{margin:calc(var(--spacing)*15)}.m-16{margin:calc(var(--spacing)*16)}.m-17{margin:calc(var(--spacing)*17)}.m-18{margin:calc(var(--spacing)*18)}.m-19{margin:calc(var(--spacing)*19)}.m-20{margin:calc(var(--spacing)*20)}.m-21{margin:calc(var(--spacing)*21)}.m-22{margin:calc(var(--spacing)*22)}.m-23{margin:calc(var(--spacing)*23)}.m-24{margin:calc(var(--spacing)*24)}.m-25{margin:calc(var(--spacing)*25)}.m-26{margin:calc(var(--spacing)*26)}.m-27{margin:calc(var(--spacing)*27)}.m-28{margin:calc(var(--spacing)*28)}.m-29{margin:calc(var(--spacing)*29)}.m-30{margin:calc(var(--spacing)*30)}.m-31{margin:calc(var(--spacing)*31)}.m-32{margin:calc(var(--spacing)*32)}.m-33{margin:calc(var(--spacing)*33)}.m-34{margin:calc(var(--spacing)*34)}.m-35{margin:calc(var(--spacing)*35)}.m-36{margin:calc(var(--spacing)*36)}.m-37{margin:calc(var(--spacing)*37)}.m-38{margin:calc(var(--spacing)*38)}.m-39{margin:calc(var(--spacing)*39)}.m-40{margin:calc(var(--spacing)*40)}.m-41{margin:calc(var(--spacing)*41)}.m-42{margin:calc(var(--spacing)*42)}.m-43{margin:calc(var(--spacing)*43)}.m-44{margin:calc(var(--spacing)*44)}.m-45{margin:calc(var(--spacing)*45)}.m-46{margin:calc(var(--spacing)*46)}.m-47{margin:calc(var(--spacing)*47)}.m-48{margin:calc(var(--spacing)*48)}.m-49{margin:calc(var(--spacing)*49)}.m-50{margin:calc(var(--spacing)*50)}.m-51{margin:calc(var(--spacing)*51)}.m-52{margin:calc(var(--spacing)*52)}.m-53{margin:calc(var(--spacing)*53)}.m-54{margin:calc(var(--spacing)*54)}.m-55{margin:calc(var(--spacing)*55)}.m-56{margin:calc(var(--spacing)*56)}.m-57{margin:calc(var(--spacing)*57)}.m-58{margin:calc(var(--spacing)*58)}.m-59{margin:calc(var(--spacing)*59)}.m-60{margin:calc(var(--spacing)*60)}.m-61{margin:calc(var(--spacing)*61)}.m-62{margin:calc(var(--spacing)*62)}.m-63{margin:calc(var(--spacing)*63)}.m-64{margin:calc(var(--spacing)*64)}.m-65{margin:calc(var(--spacing)*65)}.m-66{margin:calc(var(--spacing)*66)}.m-67{margin:calc(var(--spacing)*67)}.m-68{margin:calc(var(--spacing)*68)}.m-69{margin:calc(var(--spacing)*69)}.m-70{margin:calc(var(--spacing)*70)}.m-71{margin:calc(var(--spacing)*71)}.m-72{margin:calc(var(--spacing)*72)}.m-73{margin:calc(var(--spacing)*73)}.m-74{margin:calc(var(--spacing)*74)}.m-75{margin:calc(var(--spacing)*75)}.m-76{margin:calc(var(--spacing)*76)}.m-77{margin:calc(var(--spacing)*77)}.m-78{margin:calc(var(--spacing)*78)}.m-79{margin:calc(var(--spacing)*79)}.m-80{margin:calc(var(--spacing)*80)}.m-81{margin:calc(var(--spacing)*81)}.m-82{margin:calc(var(--spacing)*82)}.m-83{margin:calc(var(--spacing)*83)}.m-84{margin:calc(var(--spacing)*84)}.m-85{margin:calc(var(--spacing)*85)}.m-86{margin:calc(var(--spacing)*86)}.m-87{margin:calc(var(--spacing)*87)}.m-88{margin:calc(var(--spacing)*88)}.m-89{margin:calc(var(--spacing)*89)}.m-90{margin:calc(var(--spacing)*90)}.m-91{margin:calc(var(--spacing)*91)}.m-92{margin:calc(var(--spacing)*92)}.m-93{margin:calc(var(--spacing)*93)}.m-94{margin:calc(var(--spacing)*94)}.m-95{margin:calc(var(--spacing)*95)}.m-96{margin:calc(var(--spacing)*96)}.m-97{margin:calc(var(--spacing)*97)}.m-98{margin:calc(var(--spacing)*98)}.m-99{margin:calc(var(--spacing)*99)}.m-100{margin:calc(var(--spacing)*100)}.m-101{margin:calc(var(--spacing)*101)}.m-102{margin:calc(var(--spacing)*102)}.m-103{margin:calc(var(--spacing)*103)}.m-104{margin:calc(var(--spacing)*104)}.m-105{margin:calc(var(--spacing)*105)}.m-106{margin:calc(var(--spacing)*106)}.m-107{margin:calc(var(--spacing)*107)}.m-108{margin:calc(var(--spacing)*108)}.m-109{margin:calc(var(--spacing)*109)}.m-110{margin:calc(var(--spacing)*110)}.m-111{margin:calc(var(--spacing)*111)}.m-112{margin:calc(var(--spacing)*112)}.m-113{margin:calc(var(--spacing)*113)}.m-114{margin:calc(var(--spacing)*114)}.m-115{margin:calc(var(--spacing)*115)}.m-116{margin:calc(var(--spacing)*116)}.m-117{margin:calc(var(--spacing)*117)}.m-118{margin:calc(var(--spacing)*118)}.m-119{margin:calc(var(--spacing)*119)}.m-120{margin:calc(var(--spacing)*120)}.m-121{margin:calc(var(--spacing)*121)}.m-122{margin:calc(var(--spacing)*122)}.m-123{margin:calc(var(--spacing)*123)}.m-124{margin:calc(var(--spacing)*124)}.m-125{margin:calc(var(--spacing)*125)}.m-126{margin:calc(var(--spacing)*126)}.m-127{margin:calc(var(--spacing)*127)}.m-128{margin:calc(var(--spacing)*128)}.m-129{margin:calc(var(--spacing)*129)}.m-130{margin:calc(var(--spacing)*130)}.m-131{margin:calc(var(--spacing)*131)}.m-132{margin:calc(var(--spacing)*132)}.m-133{margin:calc(var(--spacing)*133)}.m-134{margin:calc(var(--spacing)*134)}.m-135{margin:calc(var(--spacing)*135)}.m-136{margin:calc(var(--spacing)*136)}.m-137{margin:calc(var(--spacing)*137)}.m-138{margin:calc(var(--spacing)*138)}.m-139{margin:calc(var(--spacing)*139)}.m-140{margin:calc(var(--spacing)*140)}.m-141{margin:calc(var(--spacing)*141)}.m-142{margin:calc(var(--spacing)*142)}.m-143{margin:calc(var(--spacing)*143)}.m-144{margin:calc(var(--spacing)*144)}.m-145{margin:calc(var(--spacing)*145)}.m-146{margin:calc(var(--spacing)*146)}.m-147{margin:calc(var(--spacing)*147)}.m-148{margin:calc(var(--spacing)*148)}.m-149{margin:calc(var(--spacing)*149)}.m-150{margin:calc(var(--spacing)*150)}.m-151{margin:calc(var(--spacing)*151)}.m-152{margin:calc(var(--spacing)*152)}.m-153{margin:calc(var(--spacing)*153)}.m-154{margin:calc(var(--spacing)*154)}.m-155{margin:calc(var(--spacing)*155)}.m-156{margin:calc(var(--spacing)*156)}.m-157{margin:calc(var(--spacing)*157)}.m-158{margin:calc(var(--spacing)*158)}.m-159{margin:calc(var(--spacing)*159)}.m-160{margin:calc(var(--spacing)*160)}.m-161{margin:calc(var(--spacing)*161)}.m-162{margin:calc(var(--spacing)*162)}.m-163{margin:calc(var(--spacing)*163)}.m-164{margin:calc(var(--spacing)*164)}.m-165{margin:calc(var(--spacing)*165)}.m-166{margin:calc(var(--spacing)*166)}.m-167{margin:calc(var(--spacing)*167)}.m-168{margin:calc(var(--spacing)*168)}.m-169{margin:calc(var(--spacing)*169)}.m-170{margin:calc(var(--spacing)*170)}.m-171{margin:calc(var(--spacing)*171)}.m-172{margin:calc(var(--spacing)*172)}.m-173{margin:calc(var(--spacing)*173)}.m-174{margin:calc(var(--spacing)*174)}.m-175{margin:calc(var(--spacing)*175)}.m-176{margin:calc(var(--spacing)*176)}.m-177{margin:calc(var(--spacing)*177)}.m-178{margin:calc(var(--spacing)*178)}.m-179{margin:calc(var(--spacing)*179)}.m-180{margin:calc(var(--spacing)*180)}.m-181{margin:calc(var(--spacing)*181)}.m-182{margin:calc(var(--spacing)*182)}.m-183{margin:calc(var(--spacing)*183)}.m-184{margin:calc(var(--spacing)*184)}.m-185{margin:calc(var(--spacing)*185)}.m-186{margin:calc(var(--spacing)*186)}.m-187{margin:calc(var(--spacing)*187)}.m-188{margin:calc(var(--spacing)*188)}.m-189{margin:calc(var(--spacing)*189)}.m-190{margin:calc(var(--spacing)*190)}.m-191{margin:calc(var(--spacing)*191)}.m-192{margin:calc(var(--spacing)*192)}.m-193{margin:calc(var(--spacing)*193)}.m-194{margin:calc(var(--spacing)*194)}.m-195{margin:calc(var(--spacing)*195)}.m-196{margin:calc(var(--spacing)*196)}.m-197{margin:calc(var(--spacing)*197)}.m-199{margin:calc(var(--spacing)*199)}.m-200{margin:calc(var(--spacing)*200)}.m-201{margin:calc(var(--spacing)*201)}.m-202{margin:calc(var(--spacing)*202)}.m-203{margin:calc(var(--spacing)*203)}.m-204{margin:calc(var(--spacing)*204)}.m-205{margin:calc(var(--spacing)*205)}.m-206{margin:calc(var(--spacing)*206)}.m-207{margin:calc(var(--spacing)*207)}.m-208{margin:calc(var(--spacing)*208)}.m-209{margin:calc(var(--spacing)*209)}.m-210{margin:calc(var(--spacing)*210)}.m-211{margin:calc(var(--spacing)*211)}.m-212{margin:calc(var(--spacing)*212)}.m-213{margin:calc(var(--spacing)*213)}.m-214{margin:calc(var(--spacing)*214)}.m-215{margin:calc(var(--spacing)*215)}.m-216{margin:calc(var(--spacing)*216)}.m-217{margin:calc(var(--spacing)*217)}.m-218{margin:calc(var(--spacing)*218)}.m-219{margin:calc(var(--spacing)*219)}.m-220{margin:calc(var(--spacing)*220)}.m-221{margin:calc(var(--spacing)*221)}.m-222{margin:calc(var(--spacing)*222)}.m-223{margin:calc(var(--spacing)*223)}.m-224{margin:calc(var(--spacing)*224)}.m-225{margin:calc(var(--spacing)*225)}.m-226{margin:calc(var(--spacing)*226)}.m-227{margin:calc(var(--spacing)*227)}.m-228{margin:calc(var(--spacing)*228)}.m-229{margin:calc(var(--spacing)*229)}.m-230{margin:calc(var(--spacing)*230)}.m-231{margin:calc(var(--spacing)*231)}.m-232{margin:calc(var(--spacing)*232)}.m-233{margin:calc(var(--spacing)*233)}.m-234{margin:calc(var(--spacing)*234)}.m-235{margin:calc(var(--spacing)*235)}.m-236{margin:calc(var(--spacing)*236)}.m-237{margin:calc(var(--spacing)*237)}.m-238{margin:calc(var(--spacing)*238)}.m-239{margin:calc(var(--spacing)*239)}.m-240{margin:calc(var(--spacing)*240)}.m-241{margin:calc(var(--spacing)*241)}.m-242{margin:calc(var(--spacing)*242)}.m-243{margin:calc(var(--spacing)*243)}.m-244{margin:calc(var(--spacing)*244)}.m-245{margin:calc(var(--spacing)*245)}.m-246{margin:calc(var(--spacing)*246)}.m-247{margin:calc(var(--spacing)*247)}.m-248{margin:calc(var(--spacing)*248)}.m-250{margin:calc(var(--spacing)*250)}.m-251{margin:calc(var(--spacing)*251)}.m-252{margin:calc(var(--spacing)*252)}.m-253{margin:calc(var(--spacing)*253)}.m-254{margin:calc(var(--spacing)*254)}.m-255{margin:calc(var(--spacing)*255)}.m-256{margin:calc(var(--spacing)*256)}.m-257{margin:calc(var(--spacing)*257)}.m-258{margin:calc(var(--spacing)*258)}.m-259{margin:calc(var(--spacing)*259)}.m-260{margin:calc(var(--spacing)*260)}.m-261{margin:calc(var(--spacing)*261)}.m-262{margin:calc(var(--spacing)*262)}.m-263{margin:calc(var(--spacing)*263)}.m-265{margin:calc(var(--spacing)*265)}.m-266{margin:calc(var(--spacing)*266)}.m-267{margin:calc(var(--spacing)*267)}.m-268{margin:calc(var(--spacing)*268)}.m-269{margin:calc(var(--spacing)*269)}.m-270{margin:calc(var(--spacing)*270)}.m-271{margin:calc(var(--spacing)*271)}.m-272{margin:calc(var(--spacing)*272)}.m-273{margin:calc(var(--spacing)*273)}.m-274{margin:calc(var(--spacing)*274)}.m-275{margin:calc(var(--spacing)*275)}.m-276{margin:calc(var(--spacing)*276)}.m-278{margin:calc(var(--spacing)*278)}.m-279{margin:calc(var(--spacing)*279)}.m-281{margin:calc(var(--spacing)*281)}.m-282{margin:calc(var(--spacing)*282)}.m-283{margin:calc(var(--spacing)*283)}.m-284{margin:calc(var(--spacing)*284)}.m-285{margin:calc(var(--spacing)*285)}.m-286{margin:calc(var(--spacing)*286)}.m-287{margin:calc(var(--spacing)*287)}.m-288{margin:calc(var(--spacing)*288)}.m-289{margin:calc(var(--spacing)*289)}.m-290{margin:calc(var(--spacing)*290)}.m-291{margin:calc(var(--spacing)*291)}.m-292{margin:calc(var(--spacing)*292)}.m-293{margin:calc(var(--spacing)*293)}.m-294{margin:calc(var(--spacing)*294)}.m-295{margin:calc(var(--spacing)*295)}.m-296{margin:calc(var(--spacing)*296)}.m-297{margin:calc(var(--spacing)*297)}.m-298{margin:calc(var(--spacing)*298)}.m-299{margin:calc(var(--spacing)*299)}.m-300{margin:calc(var(--spacing)*300)}.m-301{margin:calc(var(--spacing)*301)}.m-302{margin:calc(var(--spacing)*302)}.m-303{margin:calc(var(--spacing)*303)}.m-304{margin:calc(var(--spacing)*304)}.m-305{margin:calc(var(--spacing)*305)}.m-306{margin:calc(var(--spacing)*306)}.m-307{margin:calc(var(--spacing)*307)}.m-308{margin:calc(var(--spacing)*308)}.m-309{margin:calc(var(--spacing)*309)}.m-310{margin:calc(var(--spacing)*310)}.m-311{margin:calc(var(--spacing)*311)}.m-312{margin:calc(var(--spacing)*312)}.m-313{margin:calc(var(--spacing)*313)}.m-314{margin:calc(var(--spacing)*314)}.m-315{margin:calc(var(--spacing)*315)}.m-316{margin:calc(var(--spacing)*316)}.m-317{margin:calc(var(--spacing)*317)}.m-318{margin:calc(var(--spacing)*318)}.m-319{margin:calc(var(--spacing)*319)}.m-320{margin:calc(var(--spacing)*320)}.m-321{margin:calc(var(--spacing)*321)}.m-322{margin:calc(var(--spacing)*322)}.m-323{margin:calc(var(--spacing)*323)}.m-324{margin:calc(var(--spacing)*324)}.m-325{margin:calc(var(--spacing)*325)}.m-327{margin:calc(var(--spacing)*327)}.m-328{margin:calc(var(--spacing)*328)}.m-329{margin:calc(var(--spacing)*329)}.m-330{margin:calc(var(--spacing)*330)}.m-331{margin:calc(var(--spacing)*331)}.m-332{margin:calc(var(--spacing)*332)}.m-333{margin:calc(var(--spacing)*333)}.m-334{margin:calc(var(--spacing)*334)}.m-335{margin:calc(var(--spacing)*335)}.m-336{margin:calc(var(--spacing)*336)}.m-337{margin:calc(var(--spacing)*337)}.m-338{margin:calc(var(--spacing)*338)}.m-339{margin:calc(var(--spacing)*339)}.m-340{margin:calc(var(--spacing)*340)}.m-341{margin:calc(var(--spacing)*341)}.m-342{margin:calc(var(--spacing)*342)}.m-343{margin:calc(var(--spacing)*343)}.m-344{margin:calc(var(--spacing)*344)}.m-345{margin:calc(var(--spacing)*345)}.m-346{margin:calc(var(--spacing)*346)}.m-347{margin:calc(var(--spacing)*347)}.m-348{margin:calc(var(--spacing)*348)}.m-349{margin:calc(var(--spacing)*349)}.m-350{margin:calc(var(--spacing)*350)}.m-351{margin:calc(var(--spacing)*351)}.m-352{margin:calc(var(--spacing)*352)}.m-353{margin:calc(var(--spacing)*353)}.m-354{margin:calc(var(--spacing)*354)}.m-355{margin:calc(var(--spacing)*355)}.m-356{margin:calc(var(--spacing)*356)}.m-358{margin:calc(var(--spacing)*358)}.m-359{margin:calc(var(--spacing)*359)}.m-360{margin:calc(var(--spacing)*360)}.m-361{margin:calc(var(--spacing)*361)}.m-362{margin:calc(var(--spacing)*362)}.m-363{margin:calc(var(--spacing)*363)}.m-364{margin:calc(var(--spacing)*364)}.m-365{margin:calc(var(--spacing)*365)}.m-366{margin:calc(var(--spacing)*366)}.m-368{margin:calc(var(--spacing)*368)}.m-369{margin:calc(var(--spacing)*369)}.m-370{margin:calc(var(--spacing)*370)}.m-371{margin:calc(var(--spacing)*371)}.m-372{margin:calc(var(--spacing)*372)}.m-373{margin:calc(var(--spacing)*373)}.m-374{margin:calc(var(--spacing)*374)}.m-375{margin:calc(var(--spacing)*375)}.m-376{margin:calc(var(--spacing)*376)}.m-377{margin:calc(var(--spacing)*377)}.m-378{margin:calc(var(--spacing)*378)}.m-379{margin:calc(var(--spacing)*379)}.m-380{margin:calc(var(--spacing)*380)}.m-381{margin:calc(var(--spacing)*381)}.m-382{margin:calc(var(--spacing)*382)}.m-383{margin:calc(var(--spacing)*383)}.m-384{margin:calc(var(--spacing)*384)}.m-385{margin:calc(var(--spacing)*385)}.m-386{margin:calc(var(--spacing)*386)}.m-388{margin:calc(var(--spacing)*388)}.m-389{margin:calc(var(--spacing)*389)}.m-390{margin:calc(var(--spacing)*390)}.m-391{margin:calc(var(--spacing)*391)}.m-392{margin:calc(var(--spacing)*392)}.m-394{margin:calc(var(--spacing)*394)}.m-395{margin:calc(var(--spacing)*395)}.m-396{margin:calc(var(--spacing)*396)}.m-397{margin:calc(var(--spacing)*397)}.m-399{margin:calc(var(--spacing)*399)}.m-400{margin:calc(var(--spacing)*400)}.m-401{margin:calc(var(--spacing)*401)}.m-402{margin:calc(var(--spacing)*402)}.m-403{margin:calc(var(--spacing)*403)}.m-404{margin:calc(var(--spacing)*404)}.m-405{margin:calc(var(--spacing)*405)}.m-406{margin:calc(var(--spacing)*406)}.m-408{margin:calc(var(--spacing)*408)}.m-409{margin:calc(var(--spacing)*409)}.m-410{margin:calc(var(--spacing)*410)}.m-411{margin:calc(var(--spacing)*411)}.m-412{margin:calc(var(--spacing)*412)}.m-413{margin:calc(var(--spacing)*413)}.m-414{margin:calc(var(--spacing)*414)}.m-415{margin:calc(var(--spacing)*415)}.m-416{margin:calc(var(--spacing)*416)}.m-417{margin:calc(var(--spacing)*417)}.m-418{margin:calc(var(--spacing)*418)}.m-419{margin:calc(var(--spacing)*419)}.m-420{margin:calc(var(--spacing)*420)}.m-421{margin:calc(var(--spacing)*421)}.m-423{margin:calc(var(--spacing)*423)}.m-424{margin:calc(var(--spacing)*424)}.m-425{margin:calc(var(--spacing)*425)}.m-426{margin:calc(var(--spacing)*426)}.m-427{margin:calc(var(--spacing)*427)}.m-428{margin:calc(var(--spacing)*428)}.m-429{margin:calc(var(--spacing)*429)}.m-430{margin:calc(var(--spacing)*430)}.m-431{margin:calc(var(--spacing)*431)}.m-432{margin:calc(var(--spacing)*432)}.m-433{margin:calc(var(--spacing)*433)}.m-434{margin:calc(var(--spacing)*434)}.m-435{margin:calc(var(--spacing)*435)}.m-436{margin:calc(var(--spacing)*436)}.m-437{margin:calc(var(--spacing)*437)}.m-438{margin:calc(var(--spacing)*438)}.m-439{margin:calc(var(--spacing)*439)}.m-440{margin:calc(var(--spacing)*440)}.m-441{margin:calc(var(--spacing)*441)}.m-442{margin:calc(var(--spacing)*442)}.m-443{margin:calc(var(--spacing)*443)}.m-444{margin:calc(var(--spacing)*444)}.m-445{margin:calc(var(--spacing)*445)}.m-447{margin:calc(var(--spacing)*447)}.m-449{margin:calc(var(--spacing)*449)}.m-450{margin:calc(var(--spacing)*450)}.m-451{margin:calc(var(--spacing)*451)}.m-452{margin:calc(var(--spacing)*452)}.m-453{margin:calc(var(--spacing)*453)}.m-455{margin:calc(var(--spacing)*455)}.m-456{margin:calc(var(--spacing)*456)}.m-457{margin:calc(var(--spacing)*457)}.m-458{margin:calc(var(--spacing)*458)}.m-459{margin:calc(var(--spacing)*459)}.m-460{margin:calc(var(--spacing)*460)}.m-461{margin:calc(var(--spacing)*461)}.m-462{margin:calc(var(--spacing)*462)}.m-463{margin:calc(var(--spacing)*463)}.m-465{margin:calc(var(--spacing)*465)}.m-466{margin:calc(var(--spacing)*466)}.m-467{margin:calc(var(--spacing)*467)}.m-468{margin:calc(var(--spacing)*468)}.m-469{margin:calc(var(--spacing)*469)}.m-470{margin:calc(var(--spacing)*470)}.m-471{margin:calc(var(--spacing)*471)}.m-472{margin:calc(var(--spacing)*472)}.m-474{margin:calc(var(--spacing)*474)}.m-475{margin:calc(var(--spacing)*475)}.m-477{margin:calc(var(--spacing)*477)}.m-478{margin:calc(var(--spacing)*478)}.m-479{margin:calc(var(--spacing)*479)}.m-480{margin:calc(var(--spacing)*480)}.m-481{margin:calc(var(--spacing)*481)}.m-483{margin:calc(var(--spacing)*483)}.m-484{margin:calc(var(--spacing)*484)}.m-485{margin:calc(var(--spacing)*485)}.m-486{margin:calc(var(--spacing)*486)}.m-487{margin:calc(var(--spacing)*487)}.m-488{margin:calc(var(--spacing)*488)}.m-489{margin:calc(var(--spacing)*489)}.m-491{margin:calc(var(--spacing)*491)}.m-492{margin:calc(var(--spacing)*492)}.m-493{margin:calc(var(--spacing)*493)}.m-494{margin:calc(var(--spacing)*494)}.m-497{margin:calc(var(--spacing)*497)}.m-498{margin:calc(var(--spacing)*498)}.m-500{margin:calc(var(--spacing)*500)}.m-501{margin:calc(var(--spacing)*501)}.m-503{margin:calc(var(--spacing)*503)}.m-504{margin:calc(var(--spacing)*504)}.m-506{margin:calc(var(--spacing)*506)}.m-507{margin:calc(var(--spacing)*507)}.m-508{margin:calc(var(--spacing)*508)}.m-509{margin:calc(var(--spacing)*509)}.m-510{margin:calc(var(--spacing)*510)}.m-511{margin:calc(var(--spacing)*511)}.m-512{margin:calc(var(--spacing)*512)}.m-513{margin:calc(var(--spacing)*513)}.m-515{margin:calc(var(--spacing)*515)}.m-516{margin:calc(var(--spacing)*516)}.m-518{margin:calc(var(--spacing)*518)}.m-519{margin:calc(var(--spacing)*519)}.m-520{margin:calc(var(--spacing)*520)}.m-522{margin:calc(var(--spacing)*522)}.m-523{margin:calc(var(--spacing)*523)}.m-525{margin:calc(var(--spacing)*525)}.m-528{margin:calc(var(--spacing)*528)}.m-529{margin:calc(var(--spacing)*529)}.m-530{margin:calc(var(--spacing)*530)}.m-531{margin:calc(var(--spacing)*531)}.m-533{margin:calc(var(--spacing)*533)}.m-535{margin:calc(var(--spacing)*535)}.m-537{margin:calc(var(--spacing)*537)}.m-538{margin:calc(var(--spacing)*538)}.m-540{margin:calc(var(--spacing)*540)}.m-542{margin:calc(var(--spacing)*542)}.m-543{margin:calc(var(--spacing)*543)}.m-544{margin:calc(var(--spacing)*544)}.m-545{margin:calc(var(--spacing)*545)}.m-546{margin:calc(var(--spacing)*546)}.m-547{margin:calc(var(--spacing)*547)}.m-548{margin:calc(var(--spacing)*548)}.m-552{margin:calc(var(--spacing)*552)}.m-554{margin:calc(var(--spacing)*554)}.m-555{margin:calc(var(--spacing)*555)}.m-556{margin:calc(var(--spacing)*556)}.m-557{margin:calc(var(--spacing)*557)}.m-558{margin:calc(var(--spacing)*558)}.m-559{margin:calc(var(--spacing)*559)}.m-560{margin:calc(var(--spacing)*560)}.m-561{margin:calc(var(--spacing)*561)}.m-562{margin:calc(var(--spacing)*562)}.m-564{margin:calc(var(--spacing)*564)}.m-565{margin:calc(var(--spacing)*565)}.m-566{margin:calc(var(--spacing)*566)}.m-567{margin:calc(var(--spacing)*567)}.m-568{margin:calc(var(--spacing)*568)}.m-569{margin:calc(var(--spacing)*569)}.m-570{margin:calc(var(--spacing)*570)}.m-571{margin:calc(var(--spacing)*571)}.m-572{margin:calc(var(--spacing)*572)}.m-573{margin:calc(var(--spacing)*573)}.m-574{margin:calc(var(--spacing)*574)}.m-575{margin:calc(var(--spacing)*575)}.m-577{margin:calc(var(--spacing)*577)}.m-580{margin:calc(var(--spacing)*580)}.m-581{margin:calc(var(--spacing)*581)}.m-582{margin:calc(var(--spacing)*582)}.m-584{margin:calc(var(--spacing)*584)}.m-586{margin:calc(var(--spacing)*586)}.m-587{margin:calc(var(--spacing)*587)}.m-588{margin:calc(var(--spacing)*588)}.m-591{margin:calc(var(--spacing)*591)}.m-593{margin:calc(var(--spacing)*593)}.m-595{margin:calc(var(--spacing)*595)}.m-599{margin:calc(var(--spacing)*599)}.m-600{margin:calc(var(--spacing)*600)}.m-601{margin:calc(var(--spacing)*601)}.m-603{margin:calc(var(--spacing)*603)}.m-604{margin:calc(var(--spacing)*604)}.m-605{margin:calc(var(--spacing)*605)}.m-607{margin:calc(var(--spacing)*607)}.m-608{margin:calc(var(--spacing)*608)}.m-609{margin:calc(var(--spacing)*609)}.m-610{margin:calc(var(--spacing)*610)}.m-613{margin:calc(var(--spacing)*613)}.m-614{margin:calc(var(--spacing)*614)}.m-615{margin:calc(var(--spacing)*615)}.m-617{margin:calc(var(--spacing)*617)}.m-619{margin:calc(var(--spacing)*619)}.m-620{margin:calc(var(--spacing)*620)}.m-622{margin:calc(var(--spacing)*622)}.m-623{margin:calc(var(--spacing)*623)}.m-625{margin:calc(var(--spacing)*625)}.m-628{margin:calc(var(--spacing)*628)}.m-629{margin:calc(var(--spacing)*629)}.m-633{margin:calc(var(--spacing)*633)}.m-634{margin:calc(var(--spacing)*634)}.m-635{margin:calc(var(--spacing)*635)}.m-637{margin:calc(var(--spacing)*637)}.m-638{margin:calc(var(--spacing)*638)}.m-639{margin:calc(var(--spacing)*639)}.m-640{margin:calc(var(--spacing)*640)}.m-643{margin:calc(var(--spacing)*643)}.m-644{margin:calc(var(--spacing)*644)}.m-647{margin:calc(var(--spacing)*647)}.m-649{margin:calc(var(--spacing)*649)}.m-650{margin:calc(var(--spacing)*650)}.m-651{margin:calc(var(--spacing)*651)}.m-652{margin:calc(var(--spacing)*652)}.m-655{margin:calc(var(--spacing)*655)}.m-657{margin:calc(var(--spacing)*657)}.m-658{margin:calc(var(--spacing)*658)}.m-661{margin:calc(var(--spacing)*661)}.m-662{margin:calc(var(--spacing)*662)}.m-663{margin:calc(var(--spacing)*663)}.m-667{margin:calc(var(--spacing)*667)}.m-668{margin:calc(var(--spacing)*668)}.m-670{margin:calc(var(--spacing)*670)}.m-673{margin:calc(var(--spacing)*673)}.m-674{margin:calc(var(--spacing)*674)}.m-676{margin:calc(var(--spacing)*676)}.m-677{margin:calc(var(--spacing)*677)}.m-678{margin:calc(var(--spacing)*678)}.m-679{margin:calc(var(--spacing)*679)}.m-683{margin:calc(var(--spacing)*683)}.m-686{margin:calc(var(--spacing)*686)}.m-687{margin:calc(var(--spacing)*687)}.m-688{margin:calc(var(--spacing)*688)}.m-689{margin:calc(var(--spacing)*689)}.m-692{margin:calc(var(--spacing)*692)}.m-693{margin:calc(var(--spacing)*693)}.m-694{margin:calc(var(--spacing)*694)}.m-696{margin:calc(var(--spacing)*696)}.m-698{margin:calc(var(--spacing)*698)}.m-704{margin:calc(var(--spacing)*704)}.m-705{margin:calc(var(--spacing)*705)}.m-707{margin:calc(var(--spacing)*707)}.m-708{margin:calc(var(--spacing)*708)}.m-709{margin:calc(var(--spacing)*709)}.m-713{margin:calc(var(--spacing)*713)}.m-714{margin:calc(var(--spacing)*714)}.m-717{margin:calc(var(--spacing)*717)}.m-720{margin:calc(var(--spacing)*720)}.m-722{margin:calc(var(--spacing)*722)}.m-732{margin:calc(var(--spacing)*732)}.m-735{margin:calc(var(--spacing)*735)}.m-738{margin:calc(var(--spacing)*738)}.m-739{margin:calc(var(--spacing)*739)}.m-740{margin:calc(var(--spacing)*740)}.m-742{margin:calc(var(--spacing)*742)}.m-744{margin:calc(var(--spacing)*744)}.m-745{margin:calc(var(--spacing)*745)}.m-746{margin:calc(var(--spacing)*746)}.m-748{margin:calc(var(--spacing)*748)}.m-749{margin:calc(var(--spacing)*749)}.m-750{margin:calc(var(--spacing)*750)}.m-752{margin:calc(var(--spacing)*752)}.m-754{margin:calc(var(--spacing)*754)}.m-757{margin:calc(var(--spacing)*757)}.m-758{margin:calc(var(--spacing)*758)}.m-759{margin:calc(var(--spacing)*759)}.m-763{margin:calc(var(--spacing)*763)}.m-765{margin:calc(var(--spacing)*765)}.m-769{margin:calc(var(--spacing)*769)}.m-770{margin:calc(var(--spacing)*770)}.m-775{margin:calc(var(--spacing)*775)}.m-776{margin:calc(var(--spacing)*776)}.m-782{margin:calc(var(--spacing)*782)}.m-785{margin:calc(var(--spacing)*785)}.m-787{margin:calc(var(--spacing)*787)}.m-791{margin:calc(var(--spacing)*791)}.m-796{margin:calc(var(--spacing)*796)}.m-797{margin:calc(var(--spacing)*797)}.m-801{margin:calc(var(--spacing)*801)}.m-802{margin:calc(var(--spacing)*802)}.m-804{margin:calc(var(--spacing)*804)}.m-808{margin:calc(var(--spacing)*808)}.m-809{margin:calc(var(--spacing)*809)}.m-812{margin:calc(var(--spacing)*812)}.m-821{margin:calc(var(--spacing)*821)}.m-826{margin:calc(var(--spacing)*826)}.m-830{margin:calc(var(--spacing)*830)}.m-831{margin:calc(var(--spacing)*831)}.m-836{margin:calc(var(--spacing)*836)}.m-837{margin:calc(var(--spacing)*837)}.m-838{margin:calc(var(--spacing)*838)}.m-844{margin:calc(var(--spacing)*844)}.m-847{margin:calc(var(--spacing)*847)}.m-855{margin:calc(var(--spacing)*855)}.m-865{margin:calc(var(--spacing)*865)}.m-867{margin:calc(var(--spacing)*867)}.m-871{margin:calc(var(--spacing)*871)}.m-874{margin:calc(var(--spacing)*874)}.m-883{margin:calc(var(--spacing)*883)}.m-885{margin:calc(var(--spacing)*885)}.m-891{margin:calc(var(--spacing)*891)}.m-897{margin:calc(var(--spacing)*897)}.m-905{margin:calc(var(--spacing)*905)}.m-908{margin:calc(var(--spacing)*908)}.m-916{margin:calc(var(--spacing)*916)}.m-918{margin:calc(var(--spacing)*918)}.m-926{margin:calc(var(--spacing)*926)}.m-929{margin:calc(var(--spacing)*929)}.m-941{margin:calc(var(--spacing)*941)}.m-943{margin:calc(var(--spacing)*943)}.m-947{margin:calc(var(--spacing)*947)}.m-953{margin:calc(var(--spacing)*953)}.m-958{margin:calc(var(--spacing)*958)}.m-972{margin:calc(var(--spacing)*972)}.m-998{margin:calc(var(--spacing)*998)}.m-1000{margin:calc(var(--spacing)*1000)}.filter{flex-wrap:wrap;display:flex}.filter input[type=radio]{width:auto}.filter input{opacity:1;transition:margin .1s,opacity .3s,padding .3s,border-width .1s;overflow:hidden;scale:1}.filter input:not(:last-child){margin-inline-end:.25rem}.filter input.filter-reset{aspect-ratio:1}.filter input.filter-reset:after{content:"×"}.filter:not(:has(input:checked:not(.filter-reset))) .filter-reset,.filter:not(:has(input:checked:not(.filter-reset))) input[type=reset],.filter:has(input:checked:not(.filter-reset)) input:not(:checked,.filter-reset,input[type=reset]){opacity:0;border-width:0;width:0;margin-inline:0;padding-inline:0;scale:0}.mx-0{margin-inline:calc(var(--spacing)*0)}.mx-1{margin-inline:calc(var(--spacing)*1)}.mx-1\.5{margin-inline:calc(var(--spacing)*1.5)}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-3{margin-inline:calc(var(--spacing)*3)}.mx-4{margin-inline:calc(var(--spacing)*4)}.mx-5{margin-inline:calc(var(--spacing)*5)}.mx-6{margin-inline:calc(var(--spacing)*6)}.mx-auto{margin-inline:auto}.input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem}.input-lg[type=number]::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}.input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem}.input-sm[type=number]::-webkit-inner-spin-button{margin-block:-.5rem;margin-inline-end:-.75rem}.-my-1{margin-block:calc(var(--spacing)*-1)}.my-0{margin-block:calc(var(--spacing)*0)}.my-1{margin-block:calc(var(--spacing)*1)}.my-2{margin-block:calc(var(--spacing)*2)}.my-3{margin-block:calc(var(--spacing)*3)}.my-4{margin-block:calc(var(--spacing)*4)}.my-5{margin-block:calc(var(--spacing)*5)}.my-6{margin-block:calc(var(--spacing)*6)}.my-12{margin-block:calc(var(--spacing)*12)}.my-50{margin-block:calc(var(--spacing)*50)}.my-auto{margin-block:auto}.label{white-space:nowrap;color:currentColor;align-items:center;gap:.375rem;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.label{color:color-mix(in oklab,currentColor 60%,transparent)}}.label:has(input){cursor:pointer}.label:is(.input>*,.select>*){white-space:nowrap;height:calc(100% - .5rem);font-size:inherit;align-items:center;padding-inline:.75rem;display:flex}.label:is(.input>*,.select>*):first-child{border-inline-end:var(--border)solid currentColor;margin-inline:-.75rem .75rem}@supports (color:color-mix(in lab,red,red)){.label:is(.input>*,.select>*):first-child{border-inline-end:var(--border)solid color-mix(in oklab,currentColor 10%,#0000)}}.label:is(.input>*,.select>*):last-child{border-inline-start:var(--border)solid currentColor;margin-inline:.75rem -.75rem}@supports (color:color-mix(in lab,red,red)){.label:is(.input>*,.select>*):last-child{border-inline-start:var(--border)solid color-mix(in oklab,currentColor 10%,#0000)}}.join-item:where(:not(:first-child,:disabled,[disabled],.btn-disabled)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}.ms-0{margin-inline-start:calc(var(--spacing)*0)}.ms-1{margin-inline-start:calc(var(--spacing)*1)}.ms-2{margin-inline-start:calc(var(--spacing)*2)}.ms-3{margin-inline-start:calc(var(--spacing)*3)}.ms-4{margin-inline-start:calc(var(--spacing)*4)}.ms-6{margin-inline-start:calc(var(--spacing)*6)}.ms-auto{margin-inline-start:auto}.me-0{margin-inline-end:calc(var(--spacing)*0)}.me-1{margin-inline-end:calc(var(--spacing)*1)}.me-2{margin-inline-end:calc(var(--spacing)*2)}.me-3{margin-inline-end:calc(var(--spacing)*3)}.me-4{margin-inline-end:calc(var(--spacing)*4)}.me-5{margin-inline-end:calc(var(--spacing)*5)}.me-auto{margin-inline-end:auto}.-mt-2{margin-top:calc(var(--spacing)*-2)}.mt-0{margin-top:calc(var(--spacing)*0)}.mt-1{margin-top:calc(var(--spacing)*1)}.mt-1\.5{margin-top:calc(var(--spacing)*1.5)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-3{margin-top:calc(var(--spacing)*3)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-5{margin-top:calc(var(--spacing)*5)}.mt-6{margin-top:calc(var(--spacing)*6)}.mt-8{margin-top:calc(var(--spacing)*8)}.mt-10{margin-top:calc(var(--spacing)*10)}.mt-\[3px\]{margin-top:3px}.mt-auto{margin-top:auto}.-mr-2{margin-right:calc(var(--spacing)*-2)}.mr-1{margin-right:calc(var(--spacing)*1)}.mr-1\.5{margin-right:calc(var(--spacing)*1.5)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-2\.5{margin-right:calc(var(--spacing)*2.5)}.mr-3{margin-right:calc(var(--spacing)*3)}.mr-4{margin-right:calc(var(--spacing)*4)}.mr-5{margin-right:calc(var(--spacing)*5)}.mr-8{margin-right:calc(var(--spacing)*8)}.mr-auto{margin-right:auto}.footer-title{text-transform:uppercase;opacity:.6;margin-bottom:.5rem;font-weight:600}.-mb-0\.5{margin-bottom:calc(var(--spacing)*-.5)}.-mb-px{margin-bottom:-1px}.mb-0{margin-bottom:calc(var(--spacing)*0)}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-5{margin-bottom:calc(var(--spacing)*5)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.mb-8{margin-bottom:calc(var(--spacing)*8)}.mb-9{margin-bottom:calc(var(--spacing)*9)}.mb-10{margin-bottom:calc(var(--spacing)*10)}.mb-12{margin-bottom:calc(var(--spacing)*12)}.mb-auto{margin-bottom:auto}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-2{margin-left:calc(var(--spacing)*2)}.ml-3{margin-left:calc(var(--spacing)*3)}.ml-4{margin-left:calc(var(--spacing)*4)}.ml-5{margin-left:calc(var(--spacing)*5)}.carousel-item{box-sizing:content-box;scroll-snap-align:start;flex:none;display:flex}.status{aspect-ratio:1;border-radius:var(--radius-selector);background-color:var(--color-base-content);vertical-align:middle;color:#0000004d;background-position:50%;background-repeat:no-repeat;background-image:radial-gradient(circle at 35% 30%,oklch(1 0 0/calc(var(--depth)*.5)),#0000);width:.5rem;height:.5rem;display:inline-block;box-shadow:0 2px 3px -1px}@supports (color:color-mix(in lab,red,red)){.status{background-color:color-mix(in oklab,var(--color-base-content)20%,transparent);color:#0000004d}@supports (color:color-mix(in lab,red,red)){.status{color:color-mix(in oklab,var(--color-black)30%,transparent)}}.status{box-shadow:0 2px 3px -1px color-mix(in oklab,currentColor calc(var(--depth)*100%),#0000)}}.status\!{aspect-ratio:1!important;border-radius:var(--radius-selector)!important;background-color:var(--color-base-content)!important;vertical-align:middle!important;color:#0000004d!important;background-position:50%!important;background-repeat:no-repeat!important;background-image:radial-gradient(circle at 35% 30%,oklch(1 0 0/calc(var(--depth)*.5)),#0000)!important;width:.5rem!important;height:.5rem!important;display:inline-block!important;box-shadow:0 2px 3px -1px!important}@supports (color:color-mix(in lab,red,red)){.status\!{background-color:color-mix(in oklab,var(--color-base-content)20%,transparent)!important;color:#0000004d!important}@supports (color:color-mix(in lab,red,red)){.status\!{color:color-mix(in oklab,var(--color-black)30%,transparent)!important}}.status\!{box-shadow:0 2px 3px -1px color-mix(in oklab,currentColor calc(var(--depth)*100%),#0000)!important}}.badge{border-radius:var(--radius-selector);vertical-align:middle;color:var(--badge-fg);border:var(--border)solid var(--badge-color,var(--color-base-200));width:fit-content;padding-inline:calc(.25rem*3 - var(--border));background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);background-color:var(--badge-bg);--badge-bg:var(--badge-color,var(--color-base-100));--badge-fg:var(--color-base-content);--size:calc(var(--size-selector,.25rem)*6);height:var(--size);justify-content:center;align-items:center;gap:.5rem;font-size:.875rem;display:inline-flex}.badge.badge-outline{--badge-fg:var(--badge-color);--badge-bg:#0000;background-image:none;border-color:currentColor}.badge.badge-dash{--badge-fg:var(--badge-color);--badge-bg:#0000;background-image:none;border-style:dashed;border-color:currentColor}.badge.badge-soft{color:var(--badge-color,var(--color-base-content));background-color:var(--badge-color,var(--color-base-content));border-color:var(--badge-color,var(--color-base-content));background-image:none}@supports (color:color-mix(in lab,red,red)){.badge.badge-soft{background-color:color-mix(in oklab,var(--badge-color,var(--color-base-content))8%,var(--color-base-100));border-color:color-mix(in oklab,var(--badge-color,var(--color-base-content))10%,var(--color-base-100))}}.kbd{border-radius:var(--radius-field);background-color:var(--color-base-200);vertical-align:middle;border:var(--border)solid var(--color-base-content);border-bottom:calc(var(--border) + 1px)solid var(--color-base-content);--size:calc(var(--size-selector,.25rem)*6);height:var(--size);min-width:var(--size);justify-content:center;align-items:center;padding-left:.5em;padding-right:.5em;font-size:.875rem;display:inline-flex}@supports (color:color-mix(in lab,red,red)){.kbd{border:var(--border)solid color-mix(in srgb,var(--color-base-content)20%,#0000);border-bottom:calc(var(--border) + 1px)solid color-mix(in srgb,var(--color-base-content)20%,#0000)}}.tabs{--tabs-height:auto;--tabs-direction:row;height:var(--tabs-height);flex-wrap:wrap;flex-direction:var(--tabs-direction);display:flex}.navbar{align-items:center;width:100%;min-height:4rem;padding:.5rem;display:flex}.footer{grid-auto-flow:row;place-items:start;gap:2.5rem 1rem;width:100%;font-size:.875rem;line-height:1.25rem;display:grid}.footer>*{place-items:start;gap:.5rem;display:grid}.footer.footer-center{text-align:center;grid-auto-flow:column dense;place-items:center}.footer.footer-center>*{place-items:center}.stat{grid-template-columns:repeat(1,1fr);column-gap:1rem;width:100%;padding-block:1rem;padding-inline:1.5rem;display:inline-grid}.stat:not(:last-child){border-inline-end:var(--border)dashed currentColor;border-block-end:none}@supports (color:color-mix(in lab,red,red)){.stat:not(:last-child){border-inline-end:var(--border)dashed color-mix(in oklab,currentColor 10%,#0000)}}.card-body{padding:var(--card-p,1.5rem);font-size:var(--card-fs,.875rem);flex-direction:column;flex:auto;gap:.5rem;display:flex}.card-body :where(p){flex-grow:1}.carousel{scroll-snap-type:x mandatory;scroll-behavior:smooth;scrollbar-width:none;display:inline-flex;overflow-x:scroll}.carousel::-webkit-scrollbar{display:none}.alert{border-radius:var(--radius-box);color:var(--color-base-content);background-color:var(--alert-color,var(--color-base-200));text-align:start;border:var(--border)solid var(--color-base-200);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);box-shadow:0 3px 0 -2px oklch(100% 0 0/calc(var(--depth)*.08)) inset,0 1px #000,0 4px 3px -2px oklch(0% 0 0/calc(var(--depth)*.08));grid-template-columns:auto;grid-auto-flow:column;justify-content:start;place-items:center start;gap:1rem;padding-block:.75rem;padding-inline:1rem;font-size:.875rem;line-height:1.25rem;display:grid}@supports (color:color-mix(in lab,red,red)){.alert{box-shadow:0 3px 0 -2px oklch(100% 0 0/calc(var(--depth)*.08)) inset,0 1px color-mix(in oklab,color-mix(in oklab,#000 20%,var(--alert-color,var(--color-base-200)))calc(var(--depth)*20%),#0000),0 4px 3px -2px oklch(0% 0 0/calc(var(--depth)*.08))}}.alert:has(:nth-child(2)){grid-template-columns:auto minmax(auto,1fr)}.alert.alert-outline{color:var(--alert-color);box-shadow:none;background-color:#0000;background-image:none}.alert.alert-dash{color:var(--alert-color);box-shadow:none;background-color:#0000;background-image:none;border-style:dashed}.alert.alert-soft{color:var(--alert-color,var(--color-base-content));background:var(--alert-color,var(--color-base-content));border-color:var(--alert-color,var(--color-base-content));box-shadow:none;background-image:none}@supports (color:color-mix(in lab,red,red)){.alert.alert-soft{background:color-mix(in oklab,var(--alert-color,var(--color-base-content))8%,var(--color-base-100));border-color:color-mix(in oklab,var(--alert-color,var(--color-base-content))10%,var(--color-base-100))}}.alert\!{border-radius:var(--radius-box)!important;color:var(--color-base-content)!important;background-color:var(--alert-color,var(--color-base-200))!important;text-align:start!important;border:var(--border)solid var(--color-base-200)!important;background-size:auto,calc(var(--noise)*100%)!important;background-image:none,var(--fx-noise)!important;box-shadow:0 3px 0 -2px oklch(100% 0 0/calc(var(--depth)*.08)) inset,0 1px #000,0 4px 3px -2px oklch(0% 0 0/calc(var(--depth)*.08))!important;grid-template-columns:auto!important;grid-auto-flow:column!important;justify-content:start!important;place-items:center start!important;gap:1rem!important;padding-block:.75rem!important;padding-inline:1rem!important;font-size:.875rem!important;line-height:1.25rem!important;display:grid!important}@supports (color:color-mix(in lab,red,red)){.alert\!{box-shadow:0 3px 0 -2px oklch(100% 0 0/calc(var(--depth)*.08)) inset,0 1px color-mix(in oklab,color-mix(in oklab,#000 20%,var(--alert-color,var(--color-base-200)))calc(var(--depth)*20%),#0000),0 4px 3px -2px oklch(0% 0 0/calc(var(--depth)*.08))!important}}.alert\!:has(:nth-child(2)){grid-template-columns:auto minmax(auto,1fr)!important}.alert\!.alert-outline{color:var(--alert-color)!important;box-shadow:none!important;background-color:#0000!important;background-image:none!important}.alert\!.alert-dash{color:var(--alert-color)!important;box-shadow:none!important;background-color:#0000!important;background-image:none!important;border-style:dashed!important}.alert\!.alert-soft{color:var(--alert-color,var(--color-base-content))!important;background:var(--alert-color,var(--color-base-content))!important;border-color:var(--alert-color,var(--color-base-content))!important;box-shadow:none!important;background-image:none!important}@supports (color:color-mix(in lab,red,red)){.alert\!.alert-soft{background:color-mix(in oklab,var(--alert-color,var(--color-base-content))8%,var(--color-base-100))!important;border-color:color-mix(in oklab,var(--alert-color,var(--color-base-content))10%,var(--color-base-100))!important}}.fieldset{grid-template-columns:1fr;grid-auto-rows:max-content;gap:.375rem;padding-block:.25rem;font-size:.75rem;display:grid}.card-title{font-size:var(--cardtitle-fs,1.125rem);align-items:center;gap:.5rem;font-weight:600;display:flex}.join{--join-ss:0;--join-se:0;--join-es:0;--join-ee:0;align-items:stretch;display:inline-flex}.join :where(.join-item){border-start-start-radius:var(--join-ss,0);border-start-end-radius:var(--join-se,0);border-end-end-radius:var(--join-ee,0);border-end-start-radius:var(--join-es,0)}.join :where(.join-item) *{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}.join>.join-item:where(:first-child),.join :first-child:not(:last-child) :where(.join-item){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}.join>.join-item:where(:last-child),.join :last-child:not(:first-child) :where(.join-item){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}.join>.join-item:where(:only-child),.join :only-child :where(.join-item){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}.chat{column-gap:.75rem;padding-block:.25rem;display:grid}.avatar-group{display:flex;overflow:hidden}.avatar-group :where(.avatar){border:4px solid var(--color-base-100);border-radius:3.40282e38px;overflow:hidden}.line-clamp-1{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.line-clamp-2{-webkit-line-clamp:2;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.line-clamp-6{-webkit-line-clamp:6;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}:root .prose{--tw-prose-body:var(--color-base-content);--tw-prose-headings:var(--color-base-content);--tw-prose-lead:var(--color-base-content);--tw-prose-links:var(--color-base-content);--tw-prose-bold:var(--color-base-content);--tw-prose-counters:var(--color-base-content);--tw-prose-bullets:var(--color-base-content);--tw-prose-hr:var(--color-base-content);--tw-prose-quotes:var(--color-base-content);--tw-prose-quote-borders:var(--color-base-content);--tw-prose-captions:var(--color-base-content);--tw-prose-code:var(--color-base-content);--tw-prose-pre-code:var(--color-neutral-content);--tw-prose-pre-bg:var(--color-neutral);--tw-prose-th-borders:var(--color-base-content);--tw-prose-td-borders:var(--color-base-content);--tw-prose-kbd:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){:root .prose{--tw-prose-body:color-mix(in oklab,var(--color-base-content)80%,#0000);--tw-prose-bullets:color-mix(in oklab,var(--color-base-content)50%,#0000);--tw-prose-hr:color-mix(in oklab,var(--color-base-content)20%,#0000);--tw-prose-quote-borders:color-mix(in oklab,var(--color-base-content)20%,#0000);--tw-prose-captions:color-mix(in oklab,var(--color-base-content)50%,#0000);--tw-prose-th-borders:color-mix(in oklab,var(--color-base-content)50%,#0000);--tw-prose-td-borders:color-mix(in oklab,var(--color-base-content)20%,#0000);--tw-prose-kbd:color-mix(in oklab,var(--color-base-content)80%,#0000)}}:root .prose :where(code):not(pre>code){background-color:var(--color-base-200);border-radius:var(--radius-selector);border:var(--border)solid var(--color-base-300);font-weight:inherit;padding-inline:.5em}:root .prose :where(code):not(pre>code):before,:root .prose :where(code):not(pre>code):after{display:none}.mask{vertical-align:middle;display:inline-block;-webkit-mask-position:50%;mask-position:50%;-webkit-mask-size:contain;mask-size:contain;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}.mask\!{vertical-align:middle!important;display:inline-block!important;-webkit-mask-position:50%!important;mask-position:50%!important;-webkit-mask-size:contain!important;mask-size:contain!important;-webkit-mask-repeat:no-repeat!important;mask-repeat:no-repeat!important}.block{display:block}.contents{display:contents}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline{display:inline}.inline-block{display:inline-block}.inline-flex{display:inline-flex}.list-item{display:list-item}.table{display:table}.table-caption{display:table-caption}.table-cell{display:table-cell}.divider-vertical{--divider-m:1rem 0}.divider-vertical.divider{flex-direction:row;width:auto;height:1rem}.divider-vertical.divider:before,.divider-vertical.divider:after{width:100%;height:.125rem}.btn-circle{width:var(--size);height:var(--size);border-radius:3.40282e38px;padding-inline:0}.size-1{width:calc(var(--spacing)*1);height:calc(var(--spacing)*1)}.h-0{height:calc(var(--spacing)*0)}.h-0\.5{height:calc(var(--spacing)*.5)}.h-3{height:calc(var(--spacing)*3)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-6{height:calc(var(--spacing)*6)}.h-7{height:calc(var(--spacing)*7)}.h-8{height:calc(var(--spacing)*8)}.h-9{height:calc(var(--spacing)*9)}.h-10{height:calc(var(--spacing)*10)}.h-12{height:calc(var(--spacing)*12)}.h-14{height:calc(var(--spacing)*14)}.h-16{height:calc(var(--spacing)*16)}.h-20{height:calc(var(--spacing)*20)}.h-24{height:calc(var(--spacing)*24)}.h-32{height:calc(var(--spacing)*32)}.h-40{height:calc(var(--spacing)*40)}.h-44{height:calc(var(--spacing)*44)}.h-84{height:calc(var(--spacing)*84)}.h-90{height:calc(var(--spacing)*90)}.h-100{height:calc(var(--spacing)*100)}.h-\[18px\]{height:18px}.h-\[40px\]{height:40px}.h-\[56px\]{height:56px}.h-\[280px\]{height:280px}.h-\[500px\]{height:500px}.h-full{height:100%}.h-screen{height:100vh}.max-h-60{max-height:calc(var(--spacing)*60)}.max-h-96{max-height:calc(var(--spacing)*96)}.max-h-\[75vh\]{max-height:75vh}.max-h-\[90vh\]{max-height:90vh}.max-h-screen{max-height:100vh}.min-h-\[38px\]{min-height:38px}.min-h-\[120px\]{min-height:120px}.min-h-full{min-height:100%}.min-h-screen{min-height:100vh}.loading-lg{width:calc(var(--size-selector,.25rem)*7)}.w-1{width:calc(var(--spacing)*1)}.w-1\/2{width:50%}.w-1\/3{width:33.3333%}.w-1\/4{width:25%}.w-3{width:calc(var(--spacing)*3)}.w-3\/4{width:75%}.w-4{width:calc(var(--spacing)*4)}.w-5{width:calc(var(--spacing)*5)}.w-6{width:calc(var(--spacing)*6)}.w-7{width:calc(var(--spacing)*7)}.w-8{width:calc(var(--spacing)*8)}.w-10{width:calc(var(--spacing)*10)}.w-12{width:calc(var(--spacing)*12)}.w-14{width:calc(var(--spacing)*14)}.w-16{width:calc(var(--spacing)*16)}.w-20{width:calc(var(--spacing)*20)}.w-24{width:calc(var(--spacing)*24)}.w-25{width:calc(var(--spacing)*25)}.w-32{width:calc(var(--spacing)*32)}.w-40{width:calc(var(--spacing)*40)}.w-44{width:calc(var(--spacing)*44)}.w-48{width:calc(var(--spacing)*48)}.w-50{width:calc(var(--spacing)*50)}.w-52{width:calc(var(--spacing)*52)}.w-75{width:calc(var(--spacing)*75)}.w-80{width:calc(var(--spacing)*80)}.w-100{width:calc(var(--spacing)*100)}.w-\[18px\]{width:18px}.w-\[120px\]{width:120px}.w-\[170px\]{width:170px}.w-\[736px\]{width:736px}.w-\[calc\(90\%-30rem\)\]{width:calc(90% - 30rem)}.w-auto{width:auto}.w-full{width:100%}.w-screen{width:100vw}.max-w-1\/2{max-width:50%}.max-w-2xl{max-width:var(--container-2xl)}.max-w-3xl{max-width:var(--container-3xl)}.max-w-4xl{max-width:var(--container-4xl)}.max-w-5xl{max-width:var(--container-5xl)}.max-w-7xl{max-width:var(--container-7xl)}.max-w-\[1px\]{max-width:1px}.max-w-\[100px\]{max-width:100px}.max-w-\[250px\]{max-width:250px}.max-w-full{max-width:100%}.max-w-md{max-width:var(--container-md)}.max-w-none{max-width:none}.max-w-xl{max-width:var(--container-xl)}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-\[2rem\]{min-width:2rem}.min-w-\[100px\]{min-width:100px}.min-w-\[140px\]{min-width:140px}.min-w-\[200px\]{min-width:200px}.min-w-\[240px\]{min-width:240px}.min-w-\[300px\]{min-width:300px}.min-w-full{min-width:100%}.flex-1{flex:1}.flex-shrink{flex-shrink:1}.flex-shrink-0{flex-shrink:0}.shrink{flex-shrink:1}.shrink-0{flex-shrink:0}.flex-grow{flex-grow:1}.flex-grow-0{flex-grow:0}.flex-grow-1,.grow{flex-grow:1}.grow-0{flex-grow:0}.basis-auto{flex-basis:auto}.table-fixed{table-layout:fixed}.border-collapse{border-collapse:collapse}.border-separate{border-collapse:separate}.origin-top{transform-origin:top}.-translate-x-1\/2{--tw-translate-x: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-0{--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-full{--tw-translate-x:100%;translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\/2{--tw-translate-y: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-0{--tw-translate-y:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-4{--tw-translate-y:calc(var(--spacing)*4);translate:var(--tw-translate-x)var(--tw-translate-y)}.scale-90{--tw-scale-x:90%;--tw-scale-y:90%;--tw-scale-z:90%;scale:var(--tw-scale-x)var(--tw-scale-y)}.scale-95{--tw-scale-x:95%;--tw-scale-y:95%;--tw-scale-z:95%;scale:var(--tw-scale-x)var(--tw-scale-y)}.scale-100{--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;scale:var(--tw-scale-x)var(--tw-scale-y)}.rotate-90{rotate:90deg}.rotate-180{rotate:180deg}.rotate-270{rotate:270deg}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.skeleton{border-radius:var(--radius-box);background-color:var(--color-base-300);will-change:background-position;background-image:linear-gradient(105deg,#0000 0% 40%,var(--color-base-100)50%,#0000 60% 100%);background-position-x:-50%;background-repeat:no-repeat;background-size:200%;animation:1.8s ease-in-out infinite skeleton}@media (prefers-reduced-motion:reduce){.skeleton{transition-duration:15s}}.animate-ping{animation:var(--animate-ping)}.animate-pulse{animation:var(--animate-pulse)}.animate-spin{animation:var(--animate-spin)}.link{cursor:pointer;text-decoration-line:underline}.link:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.link:focus{outline-offset:2px;outline:2px solid #0000}}.link:focus-visible{outline-offset:2px;outline:2px solid}.cursor-default{cursor:default}.cursor-move{cursor:move}.cursor-pointer{cursor:pointer}.cursor-text{cursor:text}.resize{resize:both}.snap-x{scroll-snap-type:x var(--tw-scroll-snap-strictness)}.snap-mandatory{--tw-scroll-snap-strictness:mandatory}.list-inside{list-style-position:inside}.list-disc{list-style-type:disc}.auto-cols-max{grid-auto-columns:max-content}.grid-flow-col{grid-auto-flow:column}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-\[1fr_auto\]{grid-template-columns:1fr auto}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-row{flex-direction:row}.flex-row-reverse{flex-direction:row-reverse}.flex-nowrap{flex-wrap:nowrap}.flex-wrap{flex-wrap:wrap}.items-baseline{align-items:baseline}.items-center{align-items:center}.items-start{align-items:flex-start}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start{justify-content:flex-start}.justify-items-center{justify-items:center}.gap-1{gap:calc(var(--spacing)*1)}.gap-2{gap:calc(var(--spacing)*2)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}.gap-5{gap:calc(var(--spacing)*5)}.gap-6{gap:calc(var(--spacing)*6)}.gap-8{gap:calc(var(--spacing)*8)}.gap-50{gap:calc(var(--spacing)*50)}:where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-4>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*4)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-6>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*6)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-reverse>:not(:last-child)){--tw-space-x-reverse:1}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-visible{overflow:visible}.overflow-x-auto{overflow-x:auto}.overflow-y-auto{overflow-y:auto}.overflow-y-hidden{overflow-y:hidden}.overflow-y-scroll{overflow-y:scroll}.scroll-smooth{scroll-behavior:smooth}.tabs-box{background-color:var(--color-base-200);--tabs-box-radius:calc(var(--radius-field) + var(--radius-field) + var(--radius-field));border-radius:calc(var(--radius-field) + min(.25rem,var(--tabs-box-radius)));box-shadow:0 -.5px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 .5px oklch(0% 0 0/calc(var(--depth)*.05)) inset;padding:.25rem}.tabs-box .tab{border-radius:var(--radius-field);border-style:none}.tabs-box .tab:focus-visible,.tabs-box .tab:is(label:has(:checked:focus-visible)){outline-offset:2px}.tabs-box>:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),.tabs-box>:is(input:checked),.tabs-box>:is(label:has(:checked)){background-color:var(--tab-bg,var(--color-base-100));box-shadow:0 1px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px 1px -1px var(--color-neutral),0 1px 6px -4px var(--color-neutral)}@supports (color:color-mix(in lab,red,red)){:is(.tabs-box>:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),.tabs-box>:is(input:checked),.tabs-box>:is(label:has(:checked))){box-shadow:0 1px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px 1px -1px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*50%),#0000),0 1px 6px -4px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*100%),#0000)}}@media (forced-colors:active){:is(.tabs-box>:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),.tabs-box>:is(input:checked),.tabs-box>:is(label:has(:checked))){border:1px solid}}.rounded{border-radius:.25rem}.rounded-2xl{border-radius:var(--radius-2xl)}.rounded-\[14px\]{border-radius:14px}.rounded-box{border-radius:var(--radius-box)}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius-lg)}.rounded-md{border-radius:var(--radius-md)}.rounded-sm{border-radius:var(--radius-sm)}.rounded-xl{border-radius:var(--radius-xl)}.rounded-xs{border-radius:var(--radius-xs)}.rounded-r{border-top-right-radius:.25rem;border-bottom-right-radius:.25rem}.rounded-r-sm{border-top-right-radius:var(--radius-sm);border-bottom-right-radius:var(--radius-sm)}.rounded-br-lg{border-bottom-right-radius:var(--radius-lg)}.rounded-bl-lg{border-bottom-left-radius:var(--radius-lg)}.border{border-style:var(--tw-border-style);border-width:1px}.border-0{border-style:var(--tw-border-style);border-width:0}.border-1{border-style:var(--tw-border-style);border-width:1px}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-t-2{border-top-style:var(--tw-border-style);border-top-width:2px}.border-r-2{border-right-style:var(--tw-border-style);border-right-width:2px}.border-r-4{border-right-style:var(--tw-border-style);border-right-width:4px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.border-l-4{border-left-style:var(--tw-border-style);border-left-width:4px}.border-dashed{--tw-border-style:dashed;border-style:dashed}.border-none{--tw-border-style:none;border-style:none}.alert-error{border-color:var(--color-error);color:var(--color-error-content);--alert-color:var(--color-error)}.alert-info{border-color:var(--color-info);color:var(--color-info-content);--alert-color:var(--color-info)}.alert-success{border-color:var(--color-success);color:var(--color-success-content);--alert-color:var(--color-success)}.alert-warning{border-color:var(--color-warning);color:var(--color-warning-content);--alert-color:var(--color-warning)}.border-\[\#D5D8DF\]{border-color:#d5d8df}.border-base-200{border-color:var(--color-base-200)}.border-base-300{border-color:var(--color-base-300)}.border-blue-600{border-color:var(--color-blue-600)}.border-gray-100{border-color:var(--color-gray-100)}.border-gray-200{border-color:var(--color-gray-200)}.border-gray-300{border-color:var(--color-gray-300)}.border-green-500{border-color:var(--color-green-500)}.border-indigo-400{border-color:var(--color-indigo-400)}.border-info{border-color:var(--color-info)}.border-neutral{border-color:var(--color-neutral)}.border-primary,.border-primary\/10{border-color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.border-primary\/10{border-color:color-mix(in oklab,var(--color-primary)10%,transparent)}}.border-secondary{border-color:var(--color-secondary)}.border-success{border-color:var(--color-success)}.border-transparent{border-color:#0000}.border-warning{border-color:var(--color-warning)}.border-yellow-300{border-color:var(--color-yellow-300)}.border-t-transparent{border-top-color:#0000}.glass{-webkit-backdrop-filter:blur(var(--glass-blur,40px));backdrop-filter:blur(var(--glass-blur,40px));background-color:#0000;background-image:linear-gradient(135deg,oklch(100% 0 0/var(--glass-opacity,30%)),#0000),linear-gradient(var(--glass-reflect-degree,100deg),oklch(100% 0 0/var(--glass-reflect-opacity,5%))25%,oklch(0% 0 0/0) 25%);box-shadow:0 0 0 1px oklch(100% 0 0/var(--glass-border-opacity,20%)) inset,0 0 0 2px #0000000d;text-shadow:0 1px oklch(0% 0 0/var(--glass-text-shadow-opacity,5%));border:none}.table-zebra tbody tr:nth-child(2n),.table-zebra tbody tr:nth-child(2n) :where(.table-pin-cols tr th){background-color:var(--color-base-200)}@media (hover:hover){:is(.table-zebra tbody tr.row-hover,.table-zebra tbody tr.row-hover:nth-child(2n)):hover{background-color:var(--color-base-300)}}.bg-base-100,.bg-base-100\/80{background-color:var(--color-base-100)}@supports (color:color-mix(in lab,red,red)){.bg-base-100\/80{background-color:color-mix(in oklab,var(--color-base-100)80%,transparent)}}.bg-base-200{background-color:var(--color-base-200)}.bg-base-300{background-color:var(--color-base-300)}.bg-black{background-color:var(--color-black)}.bg-blue-100{background-color:var(--color-blue-100)}.bg-blue-400{background-color:var(--color-blue-400)}.bg-blue-600{background-color:var(--color-blue-600)}.bg-gray-50{background-color:var(--color-gray-50)}.bg-gray-100{background-color:var(--color-gray-100)}.bg-gray-200{background-color:var(--color-gray-200)}.bg-gray-500{background-color:var(--color-gray-500)}.bg-gray-700{background-color:var(--color-gray-700)}.bg-gray-800{background-color:var(--color-gray-800)}.bg-green-100{background-color:var(--color-green-100)}.bg-indigo-50{background-color:var(--color-indigo-50)}.bg-info{background-color:var(--color-info)}.bg-pink-600{background-color:var(--color-pink-600)}.bg-primary,.bg-primary\/5{background-color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.bg-primary\/5{background-color:color-mix(in oklab,var(--color-primary)5%,transparent)}}.bg-primary\/10{background-color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.bg-primary\/10{background-color:color-mix(in oklab,var(--color-primary)10%,transparent)}}.bg-red-600{background-color:var(--color-red-600)}.bg-secondary{background-color:var(--color-secondary)}.bg-secondary-content{background-color:var(--color-secondary-content)}.bg-success{background-color:var(--color-success)}.bg-transparent{background-color:#0000}.bg-warning{background-color:var(--color-warning)}.bg-white{background-color:var(--color-white)}.bg-yellow-100{background-color:var(--color-yellow-100)}.bg-linear-to-t{--tw-gradient-position:to top;background-image:linear-gradient(var(--tw-gradient-stops))}@supports (background-image:linear-gradient(in lab,red,red)){.bg-linear-to-t{--tw-gradient-position:to top in oklab}}.bg-gradient-to-b{--tw-gradient-position:to bottom in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-br{--tw-gradient-position:to bottom right in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-l{--tw-gradient-position:to left in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-t{--tw-gradient-position:to top in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.bg-\[url\(\'\/images\/pattern\.svg\'\)\]{background-image:url(/images/pattern.svg)}.bg-none{background-image:none}.from-gray-100{--tw-gradient-from:var(--color-gray-100);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-primary\/5{--tw-gradient-from:var(--color-primary);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}@supports (color:color-mix(in lab,red,red)){.from-primary\/5{--tw-gradient-from:color-mix(in oklab,var(--color-primary)5%,transparent)}}.from-primary\/10{--tw-gradient-from:var(--color-primary);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}@supports (color:color-mix(in lab,red,red)){.from-primary\/10{--tw-gradient-from:color-mix(in oklab,var(--color-primary)10%,transparent)}}.via-transparent{--tw-gradient-via:transparent;--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-base-100{--tw-gradient-to:var(--color-base-100);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-primary\/10{--tw-gradient-to:var(--color-primary);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}@supports (color:color-mix(in lab,red,red)){.to-primary\/10{--tw-gradient-to:color-mix(in oklab,var(--color-primary)10%,transparent)}}.to-transparent{--tw-gradient-to:transparent;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.loading-spinner{-webkit-mask-image:url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");mask-image:url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E")}.mask-repeat{-webkit-mask-repeat:repeat;mask-repeat:repeat}.object-contain{object-fit:contain}.object-cover{object-fit:cover}.checkbox-sm{--size:calc(var(--size-selector,.25rem)*5);padding:.1875rem}.p-0{padding:calc(var(--spacing)*0)}.p-1{padding:calc(var(--spacing)*1)}.p-2{padding:calc(var(--spacing)*2)}.p-3{padding:calc(var(--spacing)*3)}.p-4{padding:calc(var(--spacing)*4)}.p-5{padding:calc(var(--spacing)*5)}.p-6{padding:calc(var(--spacing)*6)}.p-12{padding:calc(var(--spacing)*12)}.badge-sm{--size:calc(var(--size-selector,.25rem)*5);padding-inline:calc(.25rem*2.5 - var(--border));font-size:.75rem}.px-0{padding-inline:calc(var(--spacing)*0)}.px-1{padding-inline:calc(var(--spacing)*1)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-5{padding-inline:calc(var(--spacing)*5)}.px-6{padding-inline:calc(var(--spacing)*6)}.px-8{padding-inline:calc(var(--spacing)*8)}.px-10{padding-inline:calc(var(--spacing)*10)}.py-0{padding-block:calc(var(--spacing)*0)}.py-0\.5{padding-block:calc(var(--spacing)*.5)}.py-1{padding-block:calc(var(--spacing)*1)}.py-1\.5{padding-block:calc(var(--spacing)*1.5)}.py-2{padding-block:calc(var(--spacing)*2)}.py-2\.5{padding-block:calc(var(--spacing)*2.5)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-5{padding-block:calc(var(--spacing)*5)}.py-6{padding-block:calc(var(--spacing)*6)}.py-8{padding-block:calc(var(--spacing)*8)}.py-10{padding-block:calc(var(--spacing)*10)}.py-12{padding-block:calc(var(--spacing)*12)}.py-16{padding-block:calc(var(--spacing)*16)}.ps-0{padding-inline-start:calc(var(--spacing)*0)}.ps-1{padding-inline-start:calc(var(--spacing)*1)}.ps-3{padding-inline-start:calc(var(--spacing)*3)}.ps-6{padding-inline-start:calc(var(--spacing)*6)}.pe-0{padding-inline-end:calc(var(--spacing)*0)}.pe-3{padding-inline-end:calc(var(--spacing)*3)}.pe-4{padding-inline-end:calc(var(--spacing)*4)}.pe-5{padding-inline-end:calc(var(--spacing)*5)}.pt-0{padding-top:calc(var(--spacing)*0)}.pt-1{padding-top:calc(var(--spacing)*1)}.pt-2{padding-top:calc(var(--spacing)*2)}.pt-3{padding-top:calc(var(--spacing)*3)}.pt-4{padding-top:calc(var(--spacing)*4)}.pt-5{padding-top:calc(var(--spacing)*5)}.pt-6{padding-top:calc(var(--spacing)*6)}.pt-8{padding-top:calc(var(--spacing)*8)}.pr-0{padding-right:calc(var(--spacing)*0)}.pr-1{padding-right:calc(var(--spacing)*1)}.pr-2{padding-right:calc(var(--spacing)*2)}.pr-3{padding-right:calc(var(--spacing)*3)}.pr-4{padding-right:calc(var(--spacing)*4)}.pr-6{padding-right:calc(var(--spacing)*6)}.pr-8{padding-right:calc(var(--spacing)*8)}.pr-9{padding-right:calc(var(--spacing)*9)}.pr-10{padding-right:calc(var(--spacing)*10)}.pr-12{padding-right:calc(var(--spacing)*12)}.pb-0{padding-bottom:calc(var(--spacing)*0)}.pb-1{padding-bottom:calc(var(--spacing)*1)}.pb-2{padding-bottom:calc(var(--spacing)*2)}.pb-3{padding-bottom:calc(var(--spacing)*3)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pb-5{padding-bottom:calc(var(--spacing)*5)}.pb-6{padding-bottom:calc(var(--spacing)*6)}.pb-8{padding-bottom:calc(var(--spacing)*8)}.pb-16{padding-bottom:calc(var(--spacing)*16)}.pl-3{padding-left:calc(var(--spacing)*3)}.pl-4{padding-left:calc(var(--spacing)*4)}.pl-6{padding-left:calc(var(--spacing)*6)}.pl-10{padding-left:calc(var(--spacing)*10)}.text-center{text-align:center}.text-end{text-align:end}.text-justify{text-align:justify}.text-left{text-align:left}.text-right{text-align:right}.text-start{text-align:start}.align-middle{vertical-align:middle}.align-text-bottom{vertical-align:text-bottom}.font-mono{font-family:var(--font-mono)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}.text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.text-\[8px\]{font-size:8px}.leading-5{--tw-leading:calc(var(--spacing)*5);line-height:calc(var(--spacing)*5)}.leading-6{--tw-leading:calc(var(--spacing)*6);line-height:calc(var(--spacing)*6)}.leading-8{--tw-leading:calc(var(--spacing)*8);line-height:calc(var(--spacing)*8)}.leading-relaxed{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.leading-tight{--tw-leading:var(--leading-tight);line-height:var(--leading-tight)}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-light{--tw-font-weight:var(--font-weight-light);font-weight:var(--font-weight-light)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-widest{--tw-tracking:var(--tracking-widest);letter-spacing:var(--tracking-widest)}.text-nowrap{text-wrap:nowrap}.text-wrap{text-wrap:wrap}.break-words{overflow-wrap:break-word}.whitespace-nowrap{white-space:nowrap}.whitespace-pre-line{white-space:pre-line}.checkbox-primary{color:var(--color-primary-content);--input-color:var(--color-primary)}.range-primary{color:var(--color-primary);--range-thumb:var(--color-primary-content)}.text-\[\#fff\]{color:#fff}.text-\[\#fff\]\/80{color:#fffc}.text-accent{color:var(--color-accent)}.text-base-content,.text-base-content\/30{color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){.text-base-content\/30{color:color-mix(in oklab,var(--color-base-content)30%,transparent)}}.text-base-content\/50{color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){.text-base-content\/50{color:color-mix(in oklab,var(--color-base-content)50%,transparent)}}.text-base-content\/60{color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){.text-base-content\/60{color:color-mix(in oklab,var(--color-base-content)60%,transparent)}}.text-base-content\/70{color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){.text-base-content\/70{color:color-mix(in oklab,var(--color-base-content)70%,transparent)}}.text-base-content\/80{color:var(--color-base-content)}@supports (color:color-mix(in lab,red,red)){.text-base-content\/80{color:color-mix(in oklab,var(--color-base-content)80%,transparent)}}.text-black{color:var(--color-black)}.text-blue-600{color:var(--color-blue-600)}.text-blue-700{color:var(--color-blue-700)}.text-error{color:var(--color-error)}.text-gray-400{color:var(--color-gray-400)}.text-gray-500{color:var(--color-gray-500)}.text-gray-600{color:var(--color-gray-600)}.text-gray-700{color:var(--color-gray-700)}.text-gray-800{color:var(--color-gray-800)}.text-gray-900{color:var(--color-gray-900)}.text-green-600{color:var(--color-green-600)}.text-green-700{color:var(--color-green-700)}.text-indigo-700{color:var(--color-indigo-700)}.text-info{color:var(--color-info)}.text-primary,.text-primary\/40{color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.text-primary\/40{color:color-mix(in oklab,var(--color-primary)40%,transparent)}}.text-red-500{color:var(--color-red-500)}.text-red-600{color:var(--color-red-600)}.text-secondary{color:var(--color-secondary)}.text-success{color:var(--color-success)}.text-warning{color:var(--color-warning)}.text-white{color:var(--color-white)}.text-yellow-700{color:var(--color-yellow-700)}.capitalize{text-transform:capitalize}.lowercase{text-transform:lowercase}.normal-case{text-transform:none}.uppercase{text-transform:uppercase}.italic{font-style:italic}.ordinal{--tw-ordinal:ordinal;font-variant-numeric:var(--tw-ordinal,)var(--tw-slashed-zero,)var(--tw-numeric-figure,)var(--tw-numeric-spacing,)var(--tw-numeric-fraction,)}.link-hover{text-decoration-line:none}@media (hover:hover){.link-hover:hover{text-decoration-line:underline}}.line-through{text-decoration-line:line-through}.overline{text-decoration-line:overline}.underline{text-decoration-line:underline}.opacity-0{opacity:0}.opacity-10{opacity:.1}.opacity-25{opacity:.25}.opacity-50{opacity:.5}.opacity-60{opacity:.6}.opacity-70{opacity:.7}.opacity-75{opacity:.75}.opacity-80{opacity:.8}.opacity-90{opacity:.9}.opacity-100{opacity:1}.shadow{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-none{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xl{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring,.ring-1{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-2{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-black{--tw-ring-color:var(--color-black)}.ring-primary\/20{--tw-ring-color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.ring-primary\/20{--tw-ring-color:color-mix(in oklab,var(--color-primary)20%,transparent)}}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.btn-ghost:not(.btn-active,:hover,:active:focus,:focus-visible){--btn-shadow:"";--btn-bg:#0000;--btn-border:#0000;--btn-noise:none}.btn-ghost:not(.btn-active,:hover,:active:focus,:focus-visible):not(:disabled,[disabled],.btn-disabled){--btn-fg:currentColor;outline-color:currentColor}.blur{--tw-blur:blur(8px);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.grayscale{--tw-grayscale:grayscale(100%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.invert{--tw-invert:invert(100%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-shadow{transition-property:box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.duration-75{--tw-duration:75ms;transition-duration:75ms}.duration-100{--tw-duration:.1s;transition-duration:.1s}.duration-150{--tw-duration:.15s;transition-duration:.15s}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}.ease-in{--tw-ease:var(--ease-in);transition-timing-function:var(--ease-in)}.ease-in-out{--tw-ease:var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.ease-out{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}.btn-outline:not(.btn-active,:hover,:active:focus,:focus-visible,:disabled,[disabled],.btn-disabled,:checked){--btn-shadow:"";--btn-bg:#0000;--btn-fg:var(--btn-color);--btn-border:var(--btn-color);--btn-noise:none}.btn-lg{--fontsize:1.125rem;--btn-p:1.25rem;--size:calc(var(--size-field,.25rem)*12)}.btn-sm{--fontsize:.75rem;--btn-p:.75rem;--size:calc(var(--size-field,.25rem)*8)}.btn-xs{--fontsize:.6875rem;--btn-p:.5rem;--size:calc(var(--size-field,.25rem)*6)}.badge-info{--badge-color:var(--color-info);--badge-fg:var(--color-info-content)}.badge-primary{--badge-color:var(--color-primary);--badge-fg:var(--color-primary-content)}.badge-secondary{--badge-color:var(--color-secondary);--badge-fg:var(--color-secondary-content)}.badge-success{--badge-color:var(--color-success);--badge-fg:var(--color-success-content)}.btn-info{--btn-color:var(--color-info);--btn-fg:var(--color-info-content)}.btn-neutral{--btn-color:var(--color-neutral);--btn-fg:var(--color-neutral-content)}.btn-primary{--btn-color:var(--color-primary);--btn-fg:var(--color-primary-content)}.btn-secondary{--btn-color:var(--color-secondary);--btn-fg:var(--color-secondary-content)}.btn-success{--btn-color:var(--color-success);--btn-fg:var(--color-success-content)}.btn-warning{--btn-color:var(--color-warning);--btn-fg:var(--color-warning-content)}.select-all{-webkit-user-select:all;user-select:all}.select-none{-webkit-user-select:none;user-select:none}.select-text{-webkit-user-select:text;user-select:text}.backface-hidden{backface-visibility:hidden}.select-info,.select-info:focus,.select-info:focus-within{--input-color:var(--color-info)}.group-open\:rotate-180:is(:where(.group):is([open],:popover-open,:open) *){rotate:180deg}.group-focus-within\:text-primary:is(:where(.group):focus-within *){color:var(--color-primary)}@media (hover:hover){.group-hover\:hidden:is(:where(.group):hover *){display:none}.group-hover\:inline-block:is(:where(.group):hover *){display:inline-block}.group-hover\:scale-110:is(:where(.group):hover *){--tw-scale-x:110%;--tw-scale-y:110%;--tw-scale-z:110%;scale:var(--tw-scale-x)var(--tw-scale-y)}.group-hover\:text-primary:is(:where(.group):hover *){color:var(--color-primary)}.group-hover\:underline:is(:where(.group):hover *){text-decoration-line:underline}.group-hover\:opacity-100:is(:where(.group):hover *){opacity:1}}.group-focus\:hidden:is(:where(.group):focus *){display:none}.group-focus\:inline-block:is(:where(.group):focus *){display:inline-block}@media (hover:hover){.hover\:scale-\[1\.02\]:hover{scale:1.02}.hover\:link:hover{cursor:pointer;text-decoration-line:underline}.hover\:link:hover:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.hover\:link:hover:focus{outline-offset:2px;outline:2px solid #0000}}.hover\:link:hover:focus-visible{outline-offset:2px;outline:2px solid}.hover\:border-gray-300:hover{border-color:var(--color-gray-300)}.hover\:bg-base-100:hover{background-color:var(--color-base-100)}.hover\:bg-base-200:hover{background-color:var(--color-base-200)}.hover\:bg-base-300:hover{background-color:var(--color-base-300)}.hover\:bg-blue-500:hover{background-color:var(--color-blue-500)}.hover\:bg-blue-700:hover{background-color:var(--color-blue-700)}.hover\:bg-gray-50:hover{background-color:var(--color-gray-50)}.hover\:bg-gray-100:hover{background-color:var(--color-gray-100)}.hover\:bg-gray-700:hover{background-color:var(--color-gray-700)}.hover\:bg-gray-800:hover{background-color:var(--color-gray-800)}.hover\:bg-pink-700:hover{background-color:var(--color-pink-700)}.hover\:bg-red-500:hover{background-color:var(--color-red-500)}.hover\:text-base-content:hover{color:var(--color-base-content)}.hover\:text-blue-600:hover{color:var(--color-blue-600)}.hover\:text-blue-700:hover{color:var(--color-blue-700)}.hover\:text-gray-500:hover{color:var(--color-gray-500)}.hover\:text-gray-600:hover{color:var(--color-gray-600)}.hover\:text-gray-700:hover{color:var(--color-gray-700)}.hover\:text-gray-800:hover{color:var(--color-gray-800)}.hover\:text-gray-900:hover{color:var(--color-gray-900)}.hover\:text-primary:hover{color:var(--color-primary)}.hover\:underline:hover{text-decoration-line:underline}.hover\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\:shadow-xl:hover{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\:badge-secondary:hover{--badge-color:var(--color-secondary);--badge-fg:var(--color-secondary-content)}}.focus\:border-blue-500:focus{border-color:var(--color-blue-500)}.focus\:border-gray-300:focus{border-color:var(--color-gray-300)}.focus\:border-indigo-500:focus{border-color:var(--color-indigo-500)}.focus\:border-indigo-700:focus{border-color:var(--color-indigo-700)}.focus\:border-primary:focus{border-color:var(--color-primary)}.focus\:bg-gray-50:focus{background-color:var(--color-gray-50)}.focus\:bg-gray-100:focus{background-color:var(--color-gray-100)}.focus\:bg-gray-700:focus{background-color:var(--color-gray-700)}.focus\:bg-indigo-100:focus{background-color:var(--color-indigo-100)}.focus\:text-gray-700:focus{color:var(--color-gray-700)}.focus\:text-gray-800:focus{color:var(--color-gray-800)}.focus\:text-indigo-800:focus{color:var(--color-indigo-800)}.focus\:opacity-100:focus{opacity:1}.focus\:ring-1:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-blue-500:focus{--tw-ring-color:var(--color-blue-500)}.focus\:ring-indigo-500:focus{--tw-ring-color:var(--color-indigo-500)}.focus\:ring-primary:focus,.focus\:ring-primary\/20:focus{--tw-ring-color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.focus\:ring-primary\/20:focus{--tw-ring-color:color-mix(in oklab,var(--color-primary)20%,transparent)}}.focus\:ring-red-500:focus{--tw-ring-color:var(--color-red-500)}.focus\:ring-offset-2:focus{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus\:outline-hidden:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus\:outline-hidden:focus{outline-offset:2px;outline:2px solid #0000}}.focus\:outline-primary:focus{outline-color:var(--color-primary)}.focus\:outline-none:focus{--tw-outline-style:none;outline-style:none}.focus-visible\:ring-1:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:outline-hidden:focus-visible{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus-visible\:outline-hidden:focus-visible{outline-offset:2px;outline:2px solid #0000}}.active\:bg-gray-900:active{background-color:var(--color-gray-900)}.active\:bg-red-700:active{background-color:var(--color-red-700)}.disabled\:pointer-events-none:disabled{pointer-events:none}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:opacity-25:disabled{opacity:.25}.disabled\:opacity-40:disabled{opacity:.4}.disabled\:opacity-50:disabled{opacity:.5}@media (min-width:40rem){.sm\:mx-auto{margin-inline:auto}.sm\:my-0{margin-block:calc(var(--spacing)*0)}.sm\:mb-4{margin-bottom:calc(var(--spacing)*4)}.sm\:block{display:block}.sm\:flex{display:flex}.sm\:hidden{display:none}.sm\:inline{display:inline}.sm\:max-h-\[calc\(100vh-1rem\)\]{max-height:calc(100vh - 1rem)}.sm\:w-5{width:calc(var(--spacing)*5)}.sm\:w-36{width:calc(var(--spacing)*36)}.sm\:w-auto{width:auto}.sm\:w-full{width:100%}.sm\:max-w-2xl{max-width:var(--container-2xl)}.sm\:max-w-lg{max-width:var(--container-lg)}.sm\:max-w-md{max-width:var(--container-md)}.sm\:max-w-sm{max-width:var(--container-sm)}.sm\:max-w-xl{max-width:var(--container-xl)}.sm\:translate-y-0{--tw-translate-y:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.sm\:scale-95{--tw-scale-x:95%;--tw-scale-y:95%;--tw-scale-z:95%;scale:var(--tw-scale-x)var(--tw-scale-y)}.sm\:scale-100{--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;scale:var(--tw-scale-x)var(--tw-scale-y)}.sm\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.sm\:flex-col-reverse{flex-direction:column-reverse}.sm\:flex-row{flex-direction:row}.sm\:rounded-lg{border-radius:var(--radius-lg)}.sm\:p-8{padding:calc(var(--spacing)*8)}.sm\:px-0{padding-inline:calc(var(--spacing)*0)}.sm\:px-6{padding-inline:calc(var(--spacing)*6)}.sm\:pr-0{padding-right:calc(var(--spacing)*0)}.sm\:duration-300{--tw-duration:.3s;transition-duration:.3s}}@media (min-width:48rem){.md\:fixed{position:fixed}.md\:inset-y-0{inset-block:calc(var(--spacing)*0)}.md\:right-auto{right:auto}.md\:left-0{left:calc(var(--spacing)*0)}.md\:left-auto{left:auto}.md\:col-span-3{grid-column:span 3/span 3}.md\:m-auto{margin:auto}.md\:mx-0{margin-inline:calc(var(--spacing)*0)}.md\:mx-3{margin-inline:calc(var(--spacing)*3)}.md\:mt-0{margin-top:calc(var(--spacing)*0)}.md\:mb-0{margin-bottom:calc(var(--spacing)*0)}.md\:block{display:block}.md\:flex{display:flex}.md\:hidden{display:none}.md\:inline{display:inline}.md\:max-h-96{max-height:calc(var(--spacing)*96)}.md\:max-h-\[calc\(100vh-1rem\)\]{max-height:calc(100vh - 1rem)}.md\:max-h-\[calc\(100vh-10rem\)\]{max-height:calc(100vh - 10rem)}.md\:w-28{width:calc(var(--spacing)*28)}.md\:w-88{width:calc(var(--spacing)*88)}.md\:w-\[44\%\]{width:44%}.md\:w-auto{width:auto}.md\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.md\:grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.md\:grid-cols-6{grid-template-columns:repeat(6,minmax(0,1fr))}.md\:flex-col{flex-direction:column}.md\:flex-row{flex-direction:row}.md\:items-center{align-items:center}.md\:items-start{align-items:flex-start}.md\:justify-end{justify-content:flex-end}.md\:gap-6{gap:calc(var(--spacing)*6)}:where(.md\:space-y-0>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))}.md\:px-4{padding-inline:calc(var(--spacing)*4)}.md\:py-8{padding-block:calc(var(--spacing)*8)}.md\:pt-6{padding-top:calc(var(--spacing)*6)}.md\:pb-1{padding-bottom:calc(var(--spacing)*1)}.md\:pb-12{padding-bottom:calc(var(--spacing)*12)}.md\:pl-88{padding-left:calc(var(--spacing)*88)}.md\:text-left{text-align:left}.md\:text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.md\:text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}.md\:text-5xl{font-size:var(--text-5xl);line-height:var(--tw-leading,var(--text-5xl--line-height))}.md\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.md\:opacity-75{opacity:.75}}@media (min-width:64rem){.lg\:absolute{position:absolute}.lg\:top-2{top:calc(var(--spacing)*2)}.lg\:right-0{right:calc(var(--spacing)*0)}.lg\:right-6{right:calc(var(--spacing)*6)}.lg\:left-0{left:calc(var(--spacing)*0)}.lg\:order-1{order:1}.lg\:order-2{order:2}.lg\:col-span-3{grid-column:span 3/span 3}.lg\:mx-0{margin-inline:calc(var(--spacing)*0)}.lg\:mx-8{margin-inline:calc(var(--spacing)*8)}.lg\:mt-0{margin-top:calc(var(--spacing)*0)}.lg\:mr-4{margin-right:calc(var(--spacing)*4)}.lg\:mb-0{margin-bottom:calc(var(--spacing)*0)}.lg\:block{display:block}.lg\:flex{display:flex}.lg\:hidden{display:none}.lg\:inline{display:inline}.lg\:table-cell{display:table-cell}.lg\:max-h-\[calc\(100vh-1rem\)\]{max-height:calc(100vh - 1rem)}.lg\:max-h-\[calc\(100vh-10rem\)\]{max-height:calc(100vh - 10rem)}.lg\:w-1\/4{width:25%}.lg\:w-3\/4{width:75%}.lg\:w-44{width:calc(var(--spacing)*44)}.lg\:w-\[50px\]{width:50px}.lg\:w-auto{width:auto}.lg\:flex-1{flex:1}.lg\:flex-grow{flex-grow:1}.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.lg\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.lg\:grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.lg\:flex-row{flex-direction:row}.lg\:items-center{align-items:center}.lg\:items-start{align-items:flex-start}:where(.lg\:space-y-0>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))}.lg\:px-8{padding-inline:calc(var(--spacing)*8)}.lg\:pl-2{padding-left:calc(var(--spacing)*2)}.lg\:text-left{text-align:left}.lg\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}}@media (min-width:80rem){.xl\:container{width:100%}@media (min-width:40rem){.xl\:container{max-width:40rem}}@media (min-width:48rem){.xl\:container{max-width:48rem}}@media (min-width:64rem){.xl\:container{max-width:64rem}}.xl\:container{max-width:80rem}@media (min-width:96rem){.xl\:container{max-width:96rem}}.xl\:mx-auto{margin-inline:auto}.xl\:block{display:block}.xl\:flex{display:flex}.xl\:hidden{display:none}.xl\:inline{display:inline}.xl\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.xl\:px-0{padding-inline:calc(var(--spacing)*0)}}.ltr\:origin-top-left:where(:dir(ltr),[dir=ltr],[dir=ltr] *){transform-origin:0 0}.ltr\:origin-top-right:where(:dir(ltr),[dir=ltr],[dir=ltr] *){transform-origin:100% 0}@media (min-width:48rem){.md\:ltr\:rounded-l-none:where(:dir(ltr),[dir=ltr],[dir=ltr] *){border-top-left-radius:0;border-bottom-left-radius:0}.md\:ltr\:border-l-0:where(:dir(ltr),[dir=ltr],[dir=ltr] *){border-left-style:var(--tw-border-style);border-left-width:0}}.rtl\:origin-top-left:where(:dir(rtl),[dir=rtl],[dir=rtl] *){transform-origin:0 0}.rtl\:origin-top-right:where(:dir(rtl),[dir=rtl],[dir=rtl] *){transform-origin:100% 0}.rtl\:rotate-180:where(:dir(rtl),[dir=rtl],[dir=rtl] *){rotate:180deg}:where(.rtl\:space-x-reverse:where(:dir(rtl),[dir=rtl],[dir=rtl] *)>:not(:last-child)){--tw-space-x-reverse:1}@media (prefers-color-scheme:dark){.dark\:block{display:block}.dark\:hidden{display:none}.dark\:border-\[\#33363C\]{border-color:#33363c}.dark\:border-gray-500{border-color:var(--color-gray-500)}.dark\:border-gray-600{border-color:var(--color-gray-600)}.dark\:border-gray-700{border-color:var(--color-gray-700)}.dark\:border-gray-800{border-color:var(--color-gray-800)}.dark\:border-indigo-600{border-color:var(--color-indigo-600)}.dark\:border-yellow-800{border-color:var(--color-yellow-800)}.dark\:bg-gray-200{background-color:var(--color-gray-200)}.dark\:bg-gray-700{background-color:var(--color-gray-700)}.dark\:bg-gray-800{background-color:var(--color-gray-800)}.dark\:bg-gray-900{background-color:var(--color-gray-900)}.dark\:bg-indigo-900\/50{background-color:#312c8580}@supports (color:color-mix(in lab,red,red)){.dark\:bg-indigo-900\/50{background-color:color-mix(in oklab,var(--color-indigo-900)50%,transparent)}}.dark\:bg-secondary-content{background-color:var(--color-secondary-content)}.dark\:bg-yellow-900{background-color:var(--color-yellow-900)}.dark\:from-gray-900{--tw-gradient-from:var(--color-gray-900);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.dark\:text-blue-500{color:var(--color-blue-500)}.dark\:text-gray-100{color:var(--color-gray-100)}.dark\:text-gray-200{color:var(--color-gray-200)}.dark\:text-gray-300{color:var(--color-gray-300)}.dark\:text-gray-400{color:var(--color-gray-400)}.dark\:text-gray-800{color:var(--color-gray-800)}.dark\:text-green-400{color:var(--color-green-400)}.dark\:text-green-500{color:var(--color-green-500)}.dark\:text-indigo-300{color:var(--color-indigo-300)}.dark\:text-red-400{color:var(--color-red-400)}.dark\:text-white{color:var(--color-white)}.dark\:text-yellow-400{color:var(--color-yellow-400)}.dark\:opacity-90{opacity:.9}@media (hover:hover){.dark\:hover\:border-gray-400:hover{border-color:var(--color-gray-400)}.dark\:hover\:border-gray-600:hover{border-color:var(--color-gray-600)}.dark\:hover\:border-gray-700:hover{border-color:var(--color-gray-700)}.dark\:hover\:bg-gray-600:hover{background-color:var(--color-gray-600)}.dark\:hover\:bg-gray-700:hover{background-color:var(--color-gray-700)}.dark\:hover\:bg-gray-800:hover{background-color:var(--color-gray-800)}.dark\:hover\:bg-white:hover{background-color:var(--color-white)}.dark\:hover\:text-blue-400:hover{color:var(--color-blue-400)}.dark\:hover\:text-gray-100:hover{color:var(--color-gray-100)}.dark\:hover\:text-gray-200:hover{color:var(--color-gray-200)}.dark\:hover\:text-gray-300:hover{color:var(--color-gray-300)}}.dark\:focus\:border-gray-600:focus{border-color:var(--color-gray-600)}.dark\:focus\:border-gray-700:focus{border-color:var(--color-gray-700)}.dark\:focus\:border-indigo-300:focus{border-color:var(--color-indigo-300)}.dark\:focus\:border-indigo-600:focus{border-color:var(--color-indigo-600)}.dark\:focus\:bg-gray-700:focus{background-color:var(--color-gray-700)}.dark\:focus\:bg-gray-800:focus{background-color:var(--color-gray-800)}.dark\:focus\:bg-indigo-900:focus{background-color:var(--color-indigo-900)}.dark\:focus\:bg-white:focus{background-color:var(--color-white)}.dark\:focus\:text-gray-200:focus{color:var(--color-gray-200)}.dark\:focus\:text-gray-300:focus{color:var(--color-gray-300)}.dark\:focus\:text-indigo-200:focus{color:var(--color-indigo-200)}.dark\:focus\:ring-indigo-600:focus{--tw-ring-color:var(--color-indigo-600)}.dark\:focus\:ring-offset-gray-800:focus{--tw-ring-offset-color:var(--color-gray-800)}.dark\:active\:bg-gray-300:active{background-color:var(--color-gray-300)}}.\[\&\>span\]\:line-clamp-1>span{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}}.text-onsearch{color:#6d7179}.text-primaryDark{color:#0061a4}.text-onprimary{color:#fff}.text-primaryColorLight{color:#9ecbff}.text-primarycontainer{color:#515e7b}.text-secondary{color:#535f70}.text-onsecondary{color:#fff}.text-surface{color:#f0f3f8}.text-divider{color:#d5d8df}.text-background,.text-backgroundV2{color:#f8f9ff}.text-iconsV2{color:#1e3050}.text-appBarColor{color:#fdfcff}.text-icons{color:#1e3050}.text-onIcon{color:#a5acb9}.text-iconsColorDark{color:#43474e}.text-error{color:#ba1a1a}.text-onerror{color:#fff}.text-textPrimary{color:#43474e}.text-white{color:#fff}.text-black,.text-onbackground{color:#191c20}.text-search{color:#e6e8ee}.text-filled{color:#dddee4}.text-onFilled{color:#a6a8ae}.text-shadow{color:#00000040}.text-marker{color:#fedd00}.text-onprimarycontainer{color:#f0f1f3}.dark .text-onsearch{color:#a5a8af}.dark .text-white{color:#000}.dark .text-black,.dark .text-icons{color:#fff}@keyframes dropdown{0%{opacity:0}}@keyframes progress{50%{background-position-x:-115%}}@keyframes toast{0%{opacity:0;scale:.9}to{opacity:1;scale:1}}@keyframes rating{0%,40%{filter:brightness(1.05)contrast(1.05);scale:1.1}}@keyframes radio{0%{padding:5px}50%{padding:3px}}@keyframes skeleton{0%{background-position:150%}to{background-position:-50%}}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-scroll-snap-strictness{syntax:"*";inherits:false;initial-value:proximity}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-leading{syntax:"*";inherits:false}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-ordinal{syntax:"*";inherits:false}@property --tw-slashed-zero{syntax:"*";inherits:false}@property --tw-numeric-figure{syntax:"*";inherits:false}@property --tw-numeric-spacing{syntax:"*";inherits:false}@property --tw-numeric-fraction{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(360deg)}}@keyframes ping{75%,to{opacity:0;transform:scale(2)}}@keyframes pulse{50%{opacity:.5}}
