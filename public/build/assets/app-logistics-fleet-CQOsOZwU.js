!function(){var e=$(".logistics-fleet-sidebar-body");e.length&&new PerfectScrollbar(e[0],{wheelPropagation:!1,suppressScrollX:!0}),
//!YOUR_MAPBOX_ACCESS_TOKEN_HERE!
mapboxgl.accessToken="";const t={type:"FeatureCollection",features:[{type:"Feature",properties:{iconSize:[20,42],message:"1"},geometry:{type:"Point",coordinates:[-73.999024,40.75249842]}},{type:"Feature",properties:{iconSize:[20,42],message:"2"},geometry:{type:"Point",coordinates:[-74.03,40.75699842]}},{type:"Feature",properties:{iconSize:[20,42],message:"3"},geometry:{type:"Point",coordinates:[-73.967524,40.7599842]}},{type:"Feature",properties:{iconSize:[20,42],message:"4"},geometry:{type:"Point",coordinates:[-74.0325,40.742992]}}]},r=new mapboxgl.Map({container:"map",style:"mapbox://styles/mapbox/light-v9",center:[-73.999024,40.75249842],zoom:12.25});for(const s of t.features){const e=document.createElement("div"),o=s.properties.iconSize[0],a=s.properties.iconSize[1];e.className="marker",e.insertAdjacentHTML("afterbegin",'<img src="'+assetsPath+'img/illustrations/fleet-car.png" alt="Fleet Car" width="20" class="rounded-3" id="carFleet-'+s.properties.message+'">'),e.style.width=`${o}px`,e.style.height=`${a}px`,e.style.cursor="pointer",new mapboxgl.Marker(e).setLngLat(s.geometry.coordinates).addTo(r);const n=document.getElementById("fl-"+s.properties.message),c=document.getElementById("carFleet-"+s.properties.message);n.addEventListener("click",(function(){const e=document.querySelector(".marker-focus");Helpers._hasClass("active",n)?(r.flyTo({center:t.features[s.properties.message-1].geometry.coordinates,zoom:16}),e&&Helpers._removeClass("marker-focus",e),Helpers._addClass("marker-focus",c)):Helpers._removeClass("marker-focus",c)}))}const o=document.getElementById("carFleet-1");Helpers._addClass("marker-focus",o),document.querySelector(".mapboxgl-control-container").classList.add("d-none")}();
