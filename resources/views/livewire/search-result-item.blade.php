@if (!isset($expandedResultIndex))
    @foreach ($autoCompleteResults as $result)
        @php
            $url = '#';
            if ($result['type'] === 'كتاب') {
                $url = route('front.book.show', $result['id']);
            } elseif ($result['type'] === 'مؤلف') {
                $url = route('front.authors.show', $result['id']);
            } elseif ($result['type'] === 'تصنيف') {
                $url = route('front.categories.show', $result['id']);
            } elseif ($result['type'] === 'نص') {
                $url = route('front.book.page', [
                    'id' => $result['id'],
                    'page' => $result['page'],
                    'part' => $result['part'],
                ]);
            }
        @endphp
        @if ($result['type'] === 'النصوص' || $result['type'] === 'نص')
            <div class="flex items-center justify-between p-4 w-full shadow-sm hover:bg-base-300 mb-3">
                <!-- Title + Description -->
                <div class="flex flex-col flex-grow pl-4">
                    <div class="text-start font-semibold text-base-content">
                        {{ $result['title'] }}
                    </div>
                    @if (isset($result['subtitle']))
                        <div class="text-start text-icons line-clamp-2 text-sm pb-2">
                            {!! $result['subtitle'] !!}
                        </div>
                    @endif

                    @php
                        $resultIndex = $loop->index ?? null;
                        $pageId = isset($expandedPagination['pageId']) ? $expandedPagination['pageId'] : 0;
                    @endphp

                    @if (isset($result['desc']))
                        <!-- Original Description with Read More -->
                        <div class="text-start text-sm text-onsearch">
                            <!-- circle -->
                            <i class="fas fa-circle text-[8px]"></i> <b>الصفحة {{ $result['page'] }} @if (isset($result['part']))
                                    من الجزء{{ $result['part'] }}
                                @endif:</b>
                            {!! $result['desc'] !!}
                            <span class="underline font-bold cursor-pointer hover:text-primary-600"
                                wire:click="expandBookContent('{{ $result['id'] }}', {{ $result['id'] }}, {{ $result['part'] ?? 'null' }}, {{ $resultIndex }})">قراءة
                                المزيد</span>
                        </div>
                    @endif
                </div>
            </div>
        @else
            <a href="{{ $url }}"
                class="block col-span-full hover:bg-base-300 transition-colors rounded-md mb-3 mx-3 shadow-sm">
                <div class="flex items-center justify-between p-4 w-full">
                    <!-- Title + Description -->
                    <div class="flex flex-col flex-grow pl-4">
                        <div class="text-start font-semibold text-gray-800">
                            {{ $result['title'] }}
                        </div>
                        @if (isset($result['subtitle']))
                            <div class="text-start text-gray-500 line-clamp-2 text-sm pb-2">
                                {!! $result['subtitle'] !!}
                            </div>
                        @endif
                        @if (isset($result['desc']))
                            <div class="text-start text-sm text-gray-500 ">
                                <!-- circle -->
                                @if (isset($result['page']))
                                    <i class="fas fa-circle text-[8px]"></i> <b>الصفحة {{ $result['page'] }}
                                        @if (isset($result['part']))
                                            من المجلد {{ $result['part'] }}
                                        @endif:</b>
                                @endif
                                {!! $result['desc'] !!}
                            </div>
                        @endif
                    </div>
                </div>
            </a>
        @endif
    @endforeach
@else
    <!-- Expanded Content Area -->
    <div class="border border-gray-200 rounded-lg mt-3 bg-base-100 text-start">
        <!-- Header with close button -->
        <div class="flex items-center justify-between p-3 border-b border-gray-200">
            <div class="flex-1">
                <!-- button to go to page read -->
                <a href="{{ route('front.book.pageId', ['id' => $expandedBookId, 'pageId' => $expandedPageId, 'part' => $expandedPart ?? 'null']) }}"
                    class="text-primary underline hover:text-primary-600 focus:outline-none ml-2">
                    قراءة الصفحة
                </a>
            </div>
            <button wire:click="closeExpandedContent"
                class="text-icons hover:text-base-content focus:outline-none ml-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>
        </div>

        <!-- Content Area -->
        <div class="p-3">
            @if (isset($expandedIsLoading) && $expandedIsLoading)
                <!-- Loading State -->
                <div class="flex items-center justify-center py-6">
                    <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
                    <span class="mr-2 text-icons text-sm">جاري التحميل...</span>
                </div>
            @else
                <!-- Page Header -->
                @if (isset($expandedBook) && $expandedBook && isset($expandedCurrentTitle) && $expandedCurrentTitle)
                    <div class="mb-3">
                        <div class="border-r-4 px-3 py-2 border-primary bg-base-200 rounded-r">
                            <h5 class="text-sm font-bold text-base-content" style="font-family: Ziydia-Bold;">
                                {{ $expandedCurrentTitle }}
                            </h5>
                            <p class="text-xs text-icons">
                                الصفحة {{ $expandedPage ?? '' }}
                                @if (isset($expandedPart) && $expandedPart)
                                    من الجزء {{ $expandedPart }}
                                @endif
                            </p>
                        </div>
                    </div>
                @endif

                <!-- Content Text -->
                <div class="prose prose-sm max-w-none text-base-content leading-relaxed text-sm">
                    {!! $expandedContentText ?? '' !!}
                </div>

                <!-- Navigation Controls -->
                @if (isset($expandedPagination) && $expandedPagination)
                    <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                        <button wire:click="navigateExpandedContent('prev')"
                            @if (!$expandedPagination['previousPage']) disabled @endif
                            class="flex items-center px-2 py-1 text-xs bg-base-200 hover:bg-base-300 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed">

                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                            السابق
                        </button>

                        <span class="text-xs text-icons">
                        </span>

                        <button wire:click="navigateExpandedContent('next')"
                            @if (!$expandedPagination['nextPage']) disabled @endif
                            class="flex items-center px-2 py-1 text-xs bg-base-200 hover:bg-base-300 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            التالي
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                    </div>
                @endif
            @endif
        </div>
    </div>
@endif
