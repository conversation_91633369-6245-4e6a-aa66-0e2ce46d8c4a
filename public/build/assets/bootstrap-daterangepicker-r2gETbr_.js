var e=Object.getOwnPropertyNames;import{a as t,c as s}from"./_commonjsHelpers-MdiGH4nz.js";import{r as a}from"./jquery-DFf3aua2.js";var i,n,r=(i={"assets/bootstrap-daterangepicker-r2gETbr_.js"(e,i){var n,r;
//! moment.js
//! version : 2.30.1
//! authors : <PERSON>, <PERSON><PERSON>, Moment.js contributors
//! license : MIT
//! momentjs.com
function o(){return n.apply(null,arguments)}function l(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function h(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function d(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function c(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;var t;for(t in e)if(d(e,t))return!1;return!0}function u(e){return void 0===e}function f(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function m(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function p(e,t){var s,a=[],i=e.length;for(s=0;s<i;++s)a.push(t(e[s],s));return a}function y(e,t){for(var s in t)d(t,s)&&(e[s]=t[s]);return d(t,"toString")&&(e.toString=t.toString),d(t,"valueOf")&&(e.valueOf=t.valueOf),e}function g(e,t,s,a){return Ot(e,t,s,a,!0).utc()}function _(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function D(e){var t=null,s=!1,a=e._d&&!isNaN(e._d.getTime());return a&&(t=_(e),s=r.call(t.parsedDateParts,(function(e){return null!=e})),a=t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&s),e._strict&&(a=a&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour)),null!=Object.isFrozen&&Object.isFrozen(e)?a:(e._isValid=a,e._isValid)}function k(e){var t=g(NaN);return null!=e?y(_(t),e):_(t).userInvalidated=!0,t}r=Array.prototype.some?Array.prototype.some:function(e){var t,s=Object(this),a=s.length>>>0;for(t=0;t<a;t++)if(t in s&&e.call(this,s[t],t,s))return!0;return!1};var v=o.momentProperties=[],w=!1;function Y(e,t){var s,a,i,n=v.length;if(u(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),u(t._i)||(e._i=t._i),u(t._f)||(e._f=t._f),u(t._l)||(e._l=t._l),u(t._strict)||(e._strict=t._strict),u(t._tzm)||(e._tzm=t._tzm),u(t._isUTC)||(e._isUTC=t._isUTC),u(t._offset)||(e._offset=t._offset),u(t._pf)||(e._pf=_(t)),u(t._locale)||(e._locale=t._locale),n>0)for(s=0;s<n;s++)u(i=t[a=v[s]])||(e[a]=i);return e}function M(e){Y(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===w&&(w=!0,o.updateOffset(this),w=!1)}function b(e){return e instanceof M||null!=e&&null!=e._isAMomentObject}function S(e){!1===o.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn}function C(e,t){var s=!0;return y((function(){if(null!=o.deprecationHandler&&o.deprecationHandler(null,e),s){var a,i,n,r=[],l=arguments.length;for(i=0;i<l;i++){if(a="","object"==typeof arguments[i]){for(n in a+="\n["+i+"] ",arguments[0])d(arguments[0],n)&&(a+=n+": "+arguments[0][n]+", ");a=a.slice(0,-2)}else a=arguments[i];r.push(a)}S((Array.prototype.slice.call(r).join(""),(new Error).stack)),s=!1}return t.apply(this,arguments)}),t)}var x,O={};function P(e,t){null!=o.deprecationHandler&&o.deprecationHandler(e,t),O[e]||(S(),O[e]=!0)}function N(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function T(e,t){var s,a=y({},e);for(s in t)d(t,s)&&(h(e[s])&&h(t[s])?(a[s]={},y(a[s],e[s]),y(a[s],t[s])):null!=t[s]?a[s]=t[s]:delete a[s]);for(s in e)d(e,s)&&!d(t,s)&&h(e[s])&&(a[s]=y({},a[s]));return a}function W(e){null!=e&&this.set(e)}function R(e,t,s){var a=""+Math.abs(e),i=t-a.length;return(e>=0?s?"+":"":"-")+Math.pow(10,Math.max(0,i)).toString().substr(1)+a}o.suppressDeprecationWarnings=!1,o.deprecationHandler=null,x=Object.keys?Object.keys:function(e){var t,s=[];for(t in e)d(e,t)&&s.push(t);return s};var I=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,H=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,L={},A={};function E(e,t,s,a){var i=a;"string"==typeof a&&(i=function(){return this[a]()}),e&&(A[e]=i),t&&(A[t[0]]=function(){return R(i.apply(this,arguments),t[1],t[2])}),s&&(A[s]=function(){return this.localeData().ordinal(i.apply(this,arguments),e)})}function U(e,t){return e.isValid()?(t=F(t,e.localeData()),L[t]=L[t]||function(e){var t,s,a,i=e.match(I);for(t=0,s=i.length;t<s;t++)A[i[t]]?i[t]=A[i[t]]:i[t]=(a=i[t]).match(/\[[\s\S]/)?a.replace(/^\[|\]$/g,""):a.replace(/\\/g,"");return function(t){var a,n="";for(a=0;a<s;a++)n+=N(i[a])?i[a].call(t,e):i[a];return n}}(t),L[t](e)):e.localeData().invalidDate()}function F(e,t){var s=5;function a(e){return t.longDateFormat(e)||e}for(H.lastIndex=0;s>=0&&H.test(e);)e=e.replace(H,a),H.lastIndex=0,s-=1;return e}var j={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function V(e){return"string"==typeof e?j[e]||j[e.toLowerCase()]:void 0}function G(e){var t,s,a={};for(s in e)d(e,s)&&(t=V(s))&&(a[t]=e[s]);return a}var B,z={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1},Z=/\d/,q=/\d\d/,J=/\d{3}/,Q=/\d{4}/,X=/[+-]?\d{6}/,K=/\d\d?/,ee=/\d\d\d\d?/,te=/\d\d\d\d\d\d?/,se=/\d{1,3}/,ae=/\d{1,4}/,ie=/[+-]?\d{1,6}/,ne=/\d+/,re=/[+-]?\d+/,oe=/Z|[+-]\d\d:?\d\d/gi,le=/Z|[+-]\d\d(?::?\d\d)?/gi,he=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,de=/^[1-9]\d?/,ce=/^([1-9]\d|\d)/;function ue(e,t,s){B[e]=N(t)?t:function(e,a){return e&&s?s:t}}function fe(e,t){return d(B,e)?B[e](t._strict,t._locale):new RegExp(me(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(e,t,s,a,i){return t||s||a||i}))))}function me(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function pe(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function ye(e){var t=+e,s=0;return 0!==t&&isFinite(t)&&(s=pe(t)),s}B={};var ge={};function _e(e,t){var s,a,i=t;for("string"==typeof e&&(e=[e]),f(t)&&(i=function(e,s){s[t]=ye(e)}),a=e.length,s=0;s<a;s++)ge[e[s]]=i}function De(e,t){_e(e,(function(e,s,a,i){a._w=a._w||{},t(e,a._w,a,i)}))}function ke(e,t,s){null!=t&&d(ge,e)&&ge[e](t,s._a,s,e)}function ve(e){return e%4==0&&e%100!=0||e%400==0}function we(e){return ve(e)?366:365}E("Y",0,0,(function(){var e=this.year();return e<=9999?R(e,4):"+"+e})),E(0,["YY",2],0,(function(){return this.year()%100})),E(0,["YYYY",4],0,"year"),E(0,["YYYYY",5],0,"year"),E(0,["YYYYYY",6,!0],0,"year"),ue("Y",re),ue("YY",K,q),ue("YYYY",ae,Q),ue("YYYYY",ie,X),ue("YYYYYY",ie,X),_e(["YYYYY","YYYYYY"],0),_e("YYYY",(function(e,t){t[0]=2===e.length?o.parseTwoDigitYear(e):ye(e)})),_e("YY",(function(e,t){t[0]=o.parseTwoDigitYear(e)})),_e("Y",(function(e,t){t[0]=parseInt(e,10)})),o.parseTwoDigitYear=function(e){return ye(e)+(ye(e)>68?1900:2e3)};var Ye,Me=be("FullYear",!0);function be(e,t){return function(s){return null!=s?(Ce(this,e,s),o.updateOffset(this,t),this):Se(this,e)}}function Se(e,t){if(!e.isValid())return NaN;var s=e._d,a=e._isUTC;switch(t){case"Milliseconds":return a?s.getUTCMilliseconds():s.getMilliseconds();case"Seconds":return a?s.getUTCSeconds():s.getSeconds();case"Minutes":return a?s.getUTCMinutes():s.getMinutes();case"Hours":return a?s.getUTCHours():s.getHours();case"Date":return a?s.getUTCDate():s.getDate();case"Day":return a?s.getUTCDay():s.getDay();case"Month":return a?s.getUTCMonth():s.getMonth();case"FullYear":return a?s.getUTCFullYear():s.getFullYear();default:return NaN}}function Ce(e,t,s){var a,i,n,r,o;if(e.isValid()&&!isNaN(s)){switch(a=e._d,i=e._isUTC,t){case"Milliseconds":return void(i?a.setUTCMilliseconds(s):a.setMilliseconds(s));case"Seconds":return void(i?a.setUTCSeconds(s):a.setSeconds(s));case"Minutes":return void(i?a.setUTCMinutes(s):a.setMinutes(s));case"Hours":return void(i?a.setUTCHours(s):a.setHours(s));case"Date":return void(i?a.setUTCDate(s):a.setDate(s));case"FullYear":break;default:return}n=s,r=e.month(),o=29!==(o=e.date())||1!==r||ve(n)?o:28,i?a.setUTCFullYear(n,r,o):a.setFullYear(n,r,o)}}function xe(e,t){if(isNaN(e)||isNaN(t))return NaN;var s,a=(t%(s=12)+s)%s;return e+=(t-a)/12,1===a?ve(e)?29:28:31-a%7%2}Ye=Array.prototype.indexOf?Array.prototype.indexOf:function(e){var t;for(t=0;t<this.length;++t)if(this[t]===e)return t;return-1},E("M",["MM",2],"Mo",(function(){return this.month()+1})),E("MMM",0,0,(function(e){return this.localeData().monthsShort(this,e)})),E("MMMM",0,0,(function(e){return this.localeData().months(this,e)})),ue("M",K,de),ue("MM",K,q),ue("MMM",(function(e,t){return t.monthsShortRegex(e)})),ue("MMMM",(function(e,t){return t.monthsRegex(e)})),_e(["M","MM"],(function(e,t){t[1]=ye(e)-1})),_e(["MMM","MMMM"],(function(e,t,s,a){var i=s._locale.monthsParse(e,a,s._strict);null!=i?t[1]=i:_(s).invalidMonth=e}));var Oe="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Pe="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Ne=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Te=he,We=he;function Re(e,t,s){var a,i,n,r=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],a=0;a<12;++a)n=g([2e3,a]),this._shortMonthsParse[a]=this.monthsShort(n,"").toLocaleLowerCase(),this._longMonthsParse[a]=this.months(n,"").toLocaleLowerCase();return s?"MMM"===t?-1!==(i=Ye.call(this._shortMonthsParse,r))?i:null:-1!==(i=Ye.call(this._longMonthsParse,r))?i:null:"MMM"===t?-1!==(i=Ye.call(this._shortMonthsParse,r))||-1!==(i=Ye.call(this._longMonthsParse,r))?i:null:-1!==(i=Ye.call(this._longMonthsParse,r))||-1!==(i=Ye.call(this._shortMonthsParse,r))?i:null}function Ie(e,t){if(!e.isValid())return e;if("string"==typeof t)if(/^\d+$/.test(t))t=ye(t);else if(!f(t=e.localeData().monthsParse(t)))return e;var s=t,a=e.date();return a=a<29?a:Math.min(a,xe(e.year(),s)),e._isUTC?e._d.setUTCMonth(s,a):e._d.setMonth(s,a),e}function He(e){return null!=e?(Ie(this,e),o.updateOffset(this,!0),this):Se(this,"Month")}function Le(){function e(e,t){return t.length-e.length}var t,s,a,i,n=[],r=[],o=[];for(t=0;t<12;t++)s=g([2e3,t]),a=me(this.monthsShort(s,"")),i=me(this.months(s,"")),n.push(a),r.push(i),o.push(i),o.push(a);n.sort(e),r.sort(e),o.sort(e),this._monthsRegex=new RegExp("^("+o.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+n.join("|")+")","i")}function Ae(e,t,s,a,i,n,r){var o;return e<100&&e>=0?(o=new Date(e+400,t,s,a,i,n,r),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,s,a,i,n,r),o}function Ee(e){var t,s;return e<100&&e>=0?((s=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,s)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function Ue(e,t,s){var a=7+t-s;return-(7+Ee(e,0,a).getUTCDay()-t)%7+a-1}function Fe(e,t,s,a,i){var n,r,o=1+7*(t-1)+(7+s-a)%7+Ue(e,a,i);return o<=0?r=we(n=e-1)+o:o>we(e)?(n=e+1,r=o-we(e)):(n=e,r=o),{year:n,dayOfYear:r}}function je(e,t,s){var a,i,n=Ue(e.year(),t,s),r=Math.floor((e.dayOfYear()-n-1)/7)+1;return r<1?a=r+Ve(i=e.year()-1,t,s):r>Ve(e.year(),t,s)?(a=r-Ve(e.year(),t,s),i=e.year()+1):(i=e.year(),a=r),{week:a,year:i}}function Ve(e,t,s){var a=Ue(e,t,s),i=Ue(e+1,t,s);return(we(e)-a+i)/7}function Ge(e,t){return e.slice(t,7).concat(e.slice(0,t))}E("w",["ww",2],"wo","week"),E("W",["WW",2],"Wo","isoWeek"),ue("w",K,de),ue("ww",K,q),ue("W",K,de),ue("WW",K,q),De(["w","ww","W","WW"],(function(e,t,s,a){t[a.substr(0,1)]=ye(e)})),E("d",0,"do","day"),E("dd",0,0,(function(e){return this.localeData().weekdaysMin(this,e)})),E("ddd",0,0,(function(e){return this.localeData().weekdaysShort(this,e)})),E("dddd",0,0,(function(e){return this.localeData().weekdays(this,e)})),E("e",0,0,"weekday"),E("E",0,0,"isoWeekday"),ue("d",K),ue("e",K),ue("E",K),ue("dd",(function(e,t){return t.weekdaysMinRegex(e)})),ue("ddd",(function(e,t){return t.weekdaysShortRegex(e)})),ue("dddd",(function(e,t){return t.weekdaysRegex(e)})),De(["dd","ddd","dddd"],(function(e,t,s,a){var i=s._locale.weekdaysParse(e,a,s._strict);null!=i?t.d=i:_(s).invalidWeekday=e})),De(["d","e","E"],(function(e,t,s,a){t[a]=ye(e)}));var Be="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),ze="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ze="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),$e=he,qe=he,Je=he;function Qe(e,t,s){var a,i,n,r=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],a=0;a<7;++a)n=g([2e3,1]).day(a),this._minWeekdaysParse[a]=this.weekdaysMin(n,"").toLocaleLowerCase(),this._shortWeekdaysParse[a]=this.weekdaysShort(n,"").toLocaleLowerCase(),this._weekdaysParse[a]=this.weekdays(n,"").toLocaleLowerCase();return s?"dddd"===t?-1!==(i=Ye.call(this._weekdaysParse,r))?i:null:"ddd"===t?-1!==(i=Ye.call(this._shortWeekdaysParse,r))?i:null:-1!==(i=Ye.call(this._minWeekdaysParse,r))?i:null:"dddd"===t?-1!==(i=Ye.call(this._weekdaysParse,r))||-1!==(i=Ye.call(this._shortWeekdaysParse,r))||-1!==(i=Ye.call(this._minWeekdaysParse,r))?i:null:"ddd"===t?-1!==(i=Ye.call(this._shortWeekdaysParse,r))||-1!==(i=Ye.call(this._weekdaysParse,r))||-1!==(i=Ye.call(this._minWeekdaysParse,r))?i:null:-1!==(i=Ye.call(this._minWeekdaysParse,r))||-1!==(i=Ye.call(this._weekdaysParse,r))||-1!==(i=Ye.call(this._shortWeekdaysParse,r))?i:null}function Xe(){function e(e,t){return t.length-e.length}var t,s,a,i,n,r=[],o=[],l=[],h=[];for(t=0;t<7;t++)s=g([2e3,1]).day(t),a=me(this.weekdaysMin(s,"")),i=me(this.weekdaysShort(s,"")),n=me(this.weekdays(s,"")),r.push(a),o.push(i),l.push(n),h.push(a),h.push(i),h.push(n);r.sort(e),o.sort(e),l.sort(e),h.sort(e),this._weekdaysRegex=new RegExp("^("+h.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+r.join("|")+")","i")}function Ke(){return this.hours()%12||12}function et(e,t){E(e,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)}))}function tt(e,t){return t._meridiemParse}E("H",["HH",2],0,"hour"),E("h",["hh",2],0,Ke),E("k",["kk",2],0,(function(){return this.hours()||24})),E("hmm",0,0,(function(){return""+Ke.apply(this)+R(this.minutes(),2)})),E("hmmss",0,0,(function(){return""+Ke.apply(this)+R(this.minutes(),2)+R(this.seconds(),2)})),E("Hmm",0,0,(function(){return""+this.hours()+R(this.minutes(),2)})),E("Hmmss",0,0,(function(){return""+this.hours()+R(this.minutes(),2)+R(this.seconds(),2)})),et("a",!0),et("A",!1),ue("a",tt),ue("A",tt),ue("H",K,ce),ue("h",K,de),ue("k",K,de),ue("HH",K,q),ue("hh",K,q),ue("kk",K,q),ue("hmm",ee),ue("hmmss",te),ue("Hmm",ee),ue("Hmmss",te),_e(["H","HH"],3),_e(["k","kk"],(function(e,t,s){var a=ye(e);t[3]=24===a?0:a})),_e(["a","A"],(function(e,t,s){s._isPm=s._locale.isPM(e),s._meridiem=e})),_e(["h","hh"],(function(e,t,s){t[3]=ye(e),_(s).bigHour=!0})),_e("hmm",(function(e,t,s){var a=e.length-2;t[3]=ye(e.substr(0,a)),t[4]=ye(e.substr(a)),_(s).bigHour=!0})),_e("hmmss",(function(e,t,s){var a=e.length-4,i=e.length-2;t[3]=ye(e.substr(0,a)),t[4]=ye(e.substr(a,2)),t[5]=ye(e.substr(i)),_(s).bigHour=!0})),_e("Hmm",(function(e,t,s){var a=e.length-2;t[3]=ye(e.substr(0,a)),t[4]=ye(e.substr(a))})),_e("Hmmss",(function(e,t,s){var a=e.length-4,i=e.length-2;t[3]=ye(e.substr(0,a)),t[4]=ye(e.substr(a,2)),t[5]=ye(e.substr(i))}));var st,at=be("Hours",!0),it={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Oe,monthsShort:Pe,week:{dow:0,doy:6},weekdays:Be,weekdaysMin:Ze,weekdaysShort:ze,meridiemParse:/[ap]\.?m?\.?/i},nt={},rt={};function ot(e,t){var s,a=Math.min(e.length,t.length);for(s=0;s<a;s+=1)if(e[s]!==t[s])return s;return a}function lt(e){return e?e.toLowerCase().replace("_","-"):e}function ht(e){var t=null;if(void 0===nt[e]&&void 0!==i&&i&&i.exports&&function(e){return!(!e||!e.match("^[^/\\\\]*$"))}(e))try{t=st._abbr,require("./locale/"+e),dt(t)}catch(s){nt[e]=null}return nt[e]}function dt(e,t){var s;return e&&((s=u(t)?ut(e):ct(e,t))?st=s:"undefined"!=typeof console&&console.warn),st._abbr}function ct(e,t){if(null!==t){var s,a=it;if(t.abbr=e,null!=nt[e])P("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),a=nt[e]._config;else if(null!=t.parentLocale)if(null!=nt[t.parentLocale])a=nt[t.parentLocale]._config;else{if(null==(s=ht(t.parentLocale)))return rt[t.parentLocale]||(rt[t.parentLocale]=[]),rt[t.parentLocale].push({name:e,config:t}),null;a=s._config}return nt[e]=new W(T(a,t)),rt[e]&&rt[e].forEach((function(e){ct(e.name,e.config)})),dt(e),nt[e]}return delete nt[e],null}function ut(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return st;if(!l(e)){if(t=ht(e))return t;e=[e]}return function(e){for(var t,s,a,i,n=0;n<e.length;){for(t=(i=lt(e[n]).split("-")).length,s=(s=lt(e[n+1]))?s.split("-"):null;t>0;){if(a=ht(i.slice(0,t).join("-")))return a;if(s&&s.length>=t&&ot(i,s)>=t-1)break;t--}n++}return st}(e)}function ft(e){var t,s=e._a;return s&&-2===_(e).overflow&&(t=s[1]<0||s[1]>11?1:s[2]<1||s[2]>xe(s[0],s[1])?2:s[3]<0||s[3]>24||24===s[3]&&(0!==s[4]||0!==s[5]||0!==s[6])?3:s[4]<0||s[4]>59?4:s[5]<0||s[5]>59?5:s[6]<0||s[6]>999?6:-1,_(e)._overflowDayOfYear&&(t<0||t>2)&&(t=2),_(e)._overflowWeeks&&-1===t&&(t=7),_(e)._overflowWeekday&&-1===t&&(t=8),_(e).overflow=t),e}var mt=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,pt=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,yt=/Z|[+-]\d\d(?::?\d\d)?/,gt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],_t=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],Dt=/^\/?Date\((-?\d+)/i,kt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,vt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function wt(e){var t,s,a,i,n,r,o=e._i,l=mt.exec(o)||pt.exec(o),h=gt.length,d=_t.length;if(l){for(_(e).iso=!0,t=0,s=h;t<s;t++)if(gt[t][1].exec(l[1])){i=gt[t][0],a=!1!==gt[t][2];break}if(null==i)return void(e._isValid=!1);if(l[3]){for(t=0,s=d;t<s;t++)if(_t[t][1].exec(l[3])){n=(l[2]||" ")+_t[t][0];break}if(null==n)return void(e._isValid=!1)}if(!a&&null!=n)return void(e._isValid=!1);if(l[4]){if(!yt.exec(l[4]))return void(e._isValid=!1);r="Z"}e._f=i+(n||"")+(r||""),Ct(e)}else e._isValid=!1}function Yt(e){var t=parseInt(e,10);return t<=49?2e3+t:t<=999?1900+t:t}function Mt(e){var t,s,a,i,n,r,o,l,h=kt.exec(e._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));if(h){if(s=h[4],a=h[3],i=h[2],n=h[5],r=h[6],o=h[7],l=[Yt(s),Pe.indexOf(a),parseInt(i,10),parseInt(n,10),parseInt(r,10)],o&&l.push(parseInt(o,10)),t=l,!function(e,t,s){return!e||ze.indexOf(e)===new Date(t[0],t[1],t[2]).getDay()||(_(s).weekdayMismatch=!0,s._isValid=!1,!1)}(h[1],t,e))return;e._a=t,e._tzm=function(e,t,s){if(e)return vt[e];if(t)return 0;var a=parseInt(s,10),i=a%100;return(a-i)/100*60+i}(h[8],h[9],h[10]),e._d=Ee.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),_(e).rfc2822=!0}else e._isValid=!1}function bt(e,t,s){return null!=e?e:null!=t?t:s}function St(e){var t,s,a,i,n,r=[];if(!e._d){for(a=function(e){var t=new Date(o.now());return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}(e),e._w&&null==e._a[2]&&null==e._a[1]&&function(e){var t,s,a,i,n,r,o,l,h;null!=(t=e._w).GG||null!=t.W||null!=t.E?(n=1,r=4,s=bt(t.GG,e._a[0],je(Pt(),1,4).year),a=bt(t.W,1),((i=bt(t.E,1))<1||i>7)&&(l=!0)):(n=e._locale._week.dow,r=e._locale._week.doy,h=je(Pt(),n,r),s=bt(t.gg,e._a[0],h.year),a=bt(t.w,h.week),null!=t.d?((i=t.d)<0||i>6)&&(l=!0):null!=t.e?(i=t.e+n,(t.e<0||t.e>6)&&(l=!0)):i=n),a<1||a>Ve(s,n,r)?_(e)._overflowWeeks=!0:null!=l?_(e)._overflowWeekday=!0:(o=Fe(s,a,i,n,r),e._a[0]=o.year,e._dayOfYear=o.dayOfYear)}(e),null!=e._dayOfYear&&(n=bt(e._a[0],a[0]),(e._dayOfYear>we(n)||0===e._dayOfYear)&&(_(e)._overflowDayOfYear=!0),s=Ee(n,0,e._dayOfYear),e._a[1]=s.getUTCMonth(),e._a[2]=s.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=r[t]=a[t];for(;t<7;t++)e._a[t]=r[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[3]&&0===e._a[4]&&0===e._a[5]&&0===e._a[6]&&(e._nextDay=!0,e._a[3]=0),e._d=(e._useUTC?Ee:Ae).apply(null,r),i=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[3]=24),e._w&&void 0!==e._w.d&&e._w.d!==i&&(_(e).weekdayMismatch=!0)}}function Ct(e){if(e._f!==o.ISO_8601)if(e._f!==o.RFC_2822){e._a=[],_(e).empty=!0;var t,s,a,i,n,r,l,h=""+e._i,d=h.length,c=0;for(l=(a=F(e._f,e._locale).match(I)||[]).length,t=0;t<l;t++)i=a[t],(s=(h.match(fe(i,e))||[])[0])&&((n=h.substr(0,h.indexOf(s))).length>0&&_(e).unusedInput.push(n),h=h.slice(h.indexOf(s)+s.length),c+=s.length),A[i]?(s?_(e).empty=!1:_(e).unusedTokens.push(i),ke(i,s,e)):e._strict&&!s&&_(e).unusedTokens.push(i);_(e).charsLeftOver=d-c,h.length>0&&_(e).unusedInput.push(h),e._a[3]<=12&&!0===_(e).bigHour&&e._a[3]>0&&(_(e).bigHour=void 0),_(e).parsedDateParts=e._a.slice(0),_(e).meridiem=e._meridiem,e._a[3]=(u=e._locale,f=e._a[3],null==(m=e._meridiem)?f:null!=u.meridiemHour?u.meridiemHour(f,m):null!=u.isPM?((p=u.isPM(m))&&f<12&&(f+=12),p||12!==f||(f=0),f):f),null!==(r=_(e).era)&&(e._a[0]=e._locale.erasConvertYear(r,e._a[0])),St(e),ft(e)}else Mt(e);else wt(e);var u,f,m,p}function xt(e){var t=e._i,s=e._f;return e._locale=e._locale||ut(e._l),null===t||void 0===s&&""===t?k({nullInput:!0}):("string"==typeof t&&(e._i=t=e._locale.preparse(t)),b(t)?new M(ft(t)):(m(t)?e._d=t:l(s)?function(e){var t,s,a,i,n,r,o=!1,l=e._f.length;if(0===l)return _(e).invalidFormat=!0,void(e._d=new Date(NaN));for(i=0;i<l;i++)n=0,r=!1,t=Y({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._f=e._f[i],Ct(t),D(t)&&(r=!0),n+=_(t).charsLeftOver,n+=10*_(t).unusedTokens.length,_(t).score=n,o?n<a&&(a=n,s=t):(null==a||n<a||r)&&(a=n,s=t,r&&(o=!0));y(e,s||t)}(e):s?Ct(e):function(e){var t=e._i;u(t)?e._d=new Date(o.now()):m(t)?e._d=new Date(t.valueOf()):"string"==typeof t?function(e){var t=Dt.exec(e._i);null===t?(wt(e),!1===e._isValid&&(delete e._isValid,Mt(e),!1===e._isValid&&(delete e._isValid,e._strict?e._isValid=!1:o.createFromInputFallback(e)))):e._d=new Date(+t[1])}(e):l(t)?(e._a=p(t.slice(0),(function(e){return parseInt(e,10)})),St(e)):h(t)?function(e){if(!e._d){var t=G(e._i),s=void 0===t.day?t.date:t.day;e._a=p([t.year,t.month,s,t.hour,t.minute,t.second,t.millisecond],(function(e){return e&&parseInt(e,10)})),St(e)}}(e):f(t)?e._d=new Date(t):o.createFromInputFallback(e)}(e),D(e)||(e._d=null),e))}function Ot(e,t,s,a,i){var n,r={};return!0!==t&&!1!==t||(a=t,t=void 0),!0!==s&&!1!==s||(a=s,s=void 0),(h(e)&&c(e)||l(e)&&0===e.length)&&(e=void 0),r._isAMomentObject=!0,r._useUTC=r._isUTC=i,r._l=s,r._i=e,r._f=t,r._strict=a,(n=new M(ft(xt(r))))._nextDay&&(n.add(1,"d"),n._nextDay=void 0),n}function Pt(e,t,s,a){return Ot(e,t,s,a,!1)}o.createFromInputFallback=C("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))})),o.ISO_8601=function(){},o.RFC_2822=function(){};var Nt=C("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Pt.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:k()})),Tt=C("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Pt.apply(null,arguments);return this.isValid()&&e.isValid()?e>this?this:e:k()}));function Wt(e,t){var s,a;if(1===t.length&&l(t[0])&&(t=t[0]),!t.length)return Pt();for(s=t[0],a=1;a<t.length;++a)t[a].isValid()&&!t[a][e](s)||(s=t[a]);return s}var Rt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function It(e){var t=G(e),s=t.year||0,a=t.quarter||0,i=t.month||0,n=t.week||t.isoWeek||0,r=t.day||0,o=t.hour||0,l=t.minute||0,h=t.second||0,c=t.millisecond||0;this._isValid=function(e){var t,s,a=!1,i=Rt.length;for(t in e)if(d(e,t)&&(-1===Ye.call(Rt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(s=0;s<i;++s)if(e[Rt[s]]){if(a)return!1;parseFloat(e[Rt[s]])!==ye(e[Rt[s]])&&(a=!0)}return!0}(t),this._milliseconds=+c+1e3*h+6e4*l+1e3*o*60*60,this._days=+r+7*n,this._months=+i+3*a+12*s,this._data={},this._locale=ut(),this._bubble()}function Ht(e){return e instanceof It}function Lt(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function At(e,t){E(e,0,0,(function(){var e=this.utcOffset(),s="+";return e<0&&(e=-e,s="-"),s+R(~~(e/60),2)+t+R(~~e%60,2)}))}At("Z",":"),At("ZZ",""),ue("Z",le),ue("ZZ",le),_e(["Z","ZZ"],(function(e,t,s){s._useUTC=!0,s._tzm=Ut(le,e)}));var Et=/([\+\-]|\d\d)/gi;function Ut(e,t){var s,a,i=(t||"").match(e);return null===i?null:0===(a=60*(s=((i[i.length-1]||[])+"").match(Et)||["-",0,0])[1]+ye(s[2]))?0:"+"===s[0]?a:-a}function Ft(e,t){var s,a;return t._isUTC?(s=t.clone(),a=(b(e)||m(e)?e.valueOf():Pt(e).valueOf())-s.valueOf(),s._d.setTime(s._d.valueOf()+a),o.updateOffset(s,!1),s):Pt(e).local()}function jt(e){return-Math.round(e._d.getTimezoneOffset())}function Vt(){return!!this.isValid()&&this._isUTC&&0===this._offset}o.updateOffset=function(){};var Gt=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Bt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function zt(e,t){var s,a,i,n,r,o,l=e,h=null;return Ht(e)?l={ms:e._milliseconds,d:e._days,M:e._months}:f(e)||!isNaN(+e)?(l={},t?l[t]=+e:l.milliseconds=+e):(h=Gt.exec(e))?(s="-"===h[1]?-1:1,l={y:0,d:ye(h[2])*s,h:ye(h[3])*s,m:ye(h[4])*s,s:ye(h[5])*s,ms:ye(Lt(1e3*h[6]))*s}):(h=Bt.exec(e))?(s="-"===h[1]?-1:1,l={y:Zt(h[2],s),M:Zt(h[3],s),w:Zt(h[4],s),d:Zt(h[5],s),h:Zt(h[6],s),m:Zt(h[7],s),s:Zt(h[8],s)}):null==l?l={}:"object"==typeof l&&("from"in l||"to"in l)&&(n=Pt(l.from),r=Pt(l.to),i=n.isValid()&&r.isValid()?(r=Ft(r,n),n.isBefore(r)?o=$t(n,r):((o=$t(r,n)).milliseconds=-o.milliseconds,o.months=-o.months),o):{milliseconds:0,months:0},(l={}).ms=i.milliseconds,l.M=i.months),a=new It(l),Ht(e)&&d(e,"_locale")&&(a._locale=e._locale),Ht(e)&&d(e,"_isValid")&&(a._isValid=e._isValid),a}function Zt(e,t){var s=e&&parseFloat(e.replace(",","."));return(isNaN(s)?0:s)*t}function $t(e,t){var s={};return s.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(s.months,"M").isAfter(t)&&--s.months,s.milliseconds=+t-+e.clone().add(s.months,"M"),s}function qt(e,t){return function(s,a){var i;return null===a||isNaN(+a)||(P(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),i=s,s=a,a=i),Jt(this,zt(s,a),e),this}}function Jt(e,t,s,a){var i=t._milliseconds,n=Lt(t._days),r=Lt(t._months);e.isValid()&&(a=null==a||a,r&&Ie(e,Se(e,"Month")+r*s),n&&Ce(e,"Date",Se(e,"Date")+n*s),i&&e._d.setTime(e._d.valueOf()+i*s),a&&o.updateOffset(e,n||r))}zt.fn=It.prototype,zt.invalid=function(){return zt(NaN)};var Qt=qt(1,"add"),Xt=qt(-1,"subtract");function Kt(e){return"string"==typeof e||e instanceof String}function es(e){return b(e)||m(e)||Kt(e)||f(e)||function(e){var t=l(e),s=!1;return t&&(s=0===e.filter((function(t){return!f(t)&&Kt(e)})).length),t&&s}(e)||function(e){var t,s,a=h(e)&&!c(e),i=!1,n=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],r=n.length;for(t=0;t<r;t+=1)s=n[t],i=i||d(e,s);return a&&i}(e)||null==e}function ts(e,t){if(e.date()<t.date())return-ts(t,e);var s=12*(t.year()-e.year())+(t.month()-e.month()),a=e.clone().add(s,"months");return-(s+(t-a<0?(t-a)/(a-e.clone().add(s-1,"months")):(t-a)/(e.clone().add(s+1,"months")-a)))||0}function ss(e){var t;return void 0===e?this._locale._abbr:(null!=(t=ut(e))&&(this._locale=t),this)}o.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",o.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var as=C("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(e){return void 0===e?this.localeData():this.locale(e)}));function is(){return this._locale}var ns=1e3,rs=6e4,os=36e5,ls=126227808e5;function hs(e,t){return(e%t+t)%t}function ds(e,t,s){return e<100&&e>=0?new Date(e+400,t,s)-ls:new Date(e,t,s).valueOf()}function cs(e,t,s){return e<100&&e>=0?Date.UTC(e+400,t,s)-ls:Date.UTC(e,t,s)}function us(e,t){return t.erasAbbrRegex(e)}function fs(){var e,t,s,a,i,n=[],r=[],o=[],l=[],h=this.eras();for(e=0,t=h.length;e<t;++e)s=me(h[e].name),a=me(h[e].abbr),i=me(h[e].narrow),r.push(s),n.push(a),o.push(i),l.push(s),l.push(a),l.push(i);this._erasRegex=new RegExp("^("+l.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+r.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+n.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+o.join("|")+")","i")}function ms(e,t){E(0,[e,e.length],0,t)}function ps(e,t,s,a,i){var n;return null==e?je(this,a,i).year:(t>(n=Ve(e,a,i))&&(t=n),ys.call(this,e,t,s,a,i))}function ys(e,t,s,a,i){var n=Fe(e,t,s,a,i),r=Ee(n.year,0,n.dayOfYear);return this.year(r.getUTCFullYear()),this.month(r.getUTCMonth()),this.date(r.getUTCDate()),this}E("N",0,0,"eraAbbr"),E("NN",0,0,"eraAbbr"),E("NNN",0,0,"eraAbbr"),E("NNNN",0,0,"eraName"),E("NNNNN",0,0,"eraNarrow"),E("y",["y",1],"yo","eraYear"),E("y",["yy",2],0,"eraYear"),E("y",["yyy",3],0,"eraYear"),E("y",["yyyy",4],0,"eraYear"),ue("N",us),ue("NN",us),ue("NNN",us),ue("NNNN",(function(e,t){return t.erasNameRegex(e)})),ue("NNNNN",(function(e,t){return t.erasNarrowRegex(e)})),_e(["N","NN","NNN","NNNN","NNNNN"],(function(e,t,s,a){var i=s._locale.erasParse(e,a,s._strict);i?_(s).era=i:_(s).invalidEra=e})),ue("y",ne),ue("yy",ne),ue("yyy",ne),ue("yyyy",ne),ue("yo",(function(e,t){return t._eraYearOrdinalRegex||ne})),_e(["y","yy","yyy","yyyy"],0),_e(["yo"],(function(e,t,s,a){var i;s._locale._eraYearOrdinalRegex&&(i=e.match(s._locale._eraYearOrdinalRegex)),s._locale.eraYearOrdinalParse?t[0]=s._locale.eraYearOrdinalParse(e,i):t[0]=parseInt(e,10)})),E(0,["gg",2],0,(function(){return this.weekYear()%100})),E(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),ms("gggg","weekYear"),ms("ggggg","weekYear"),ms("GGGG","isoWeekYear"),ms("GGGGG","isoWeekYear"),ue("G",re),ue("g",re),ue("GG",K,q),ue("gg",K,q),ue("GGGG",ae,Q),ue("gggg",ae,Q),ue("GGGGG",ie,X),ue("ggggg",ie,X),De(["gggg","ggggg","GGGG","GGGGG"],(function(e,t,s,a){t[a.substr(0,2)]=ye(e)})),De(["gg","GG"],(function(e,t,s,a){t[a]=o.parseTwoDigitYear(e)})),E("Q",0,"Qo","quarter"),ue("Q",Z),_e("Q",(function(e,t){t[1]=3*(ye(e)-1)})),E("D",["DD",2],"Do","date"),ue("D",K,de),ue("DD",K,q),ue("Do",(function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient})),_e(["D","DD"],2),_e("Do",(function(e,t){t[2]=ye(e.match(K)[0])}));var gs=be("Date",!0);E("DDD",["DDDD",3],"DDDo","dayOfYear"),ue("DDD",se),ue("DDDD",J),_e(["DDD","DDDD"],(function(e,t,s){s._dayOfYear=ye(e)})),E("m",["mm",2],0,"minute"),ue("m",K,ce),ue("mm",K,q),_e(["m","mm"],4);var _s=be("Minutes",!1);E("s",["ss",2],0,"second"),ue("s",K,ce),ue("ss",K,q),_e(["s","ss"],5);var Ds,ks,vs=be("Seconds",!1);for(E("S",0,0,(function(){return~~(this.millisecond()/100)})),E(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),E(0,["SSS",3],0,"millisecond"),E(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),E(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),E(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),E(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),E(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),E(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),ue("S",se,Z),ue("SS",se,q),ue("SSS",se,J),Ds="SSSS";Ds.length<=9;Ds+="S")ue(Ds,ne);function ws(e,t){t[6]=ye(1e3*("0."+e))}for(Ds="S";Ds.length<=9;Ds+="S")_e(Ds,ws);ks=be("Milliseconds",!1),E("z",0,0,"zoneAbbr"),E("zz",0,0,"zoneName");var Ys=M.prototype;function Ms(e){return e}Ys.add=Qt,Ys.calendar=function(e,t){1===arguments.length&&(arguments[0]?es(arguments[0])?(e=arguments[0],t=void 0):function(e){var t,s=h(e)&&!c(e),a=!1,i=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"];for(t=0;t<i.length;t+=1)a=a||d(e,i[t]);return s&&a}(arguments[0])&&(t=arguments[0],e=void 0):(e=void 0,t=void 0));var s=e||Pt(),a=Ft(s,this).startOf("day"),i=o.calendarFormat(this,a)||"sameElse",n=t&&(N(t[i])?t[i].call(this,s):t[i]);return this.format(n||this.localeData().calendar(i,this,Pt(s)))},Ys.clone=function(){return new M(this)},Ys.diff=function(e,t,s){var a,i,n;if(!this.isValid())return NaN;if(!(a=Ft(e,this)).isValid())return NaN;switch(i=6e4*(a.utcOffset()-this.utcOffset()),t=V(t)){case"year":n=ts(this,a)/12;break;case"month":n=ts(this,a);break;case"quarter":n=ts(this,a)/3;break;case"second":n=(this-a)/1e3;break;case"minute":n=(this-a)/6e4;break;case"hour":n=(this-a)/36e5;break;case"day":n=(this-a-i)/864e5;break;case"week":n=(this-a-i)/6048e5;break;default:n=this-a}return s?n:pe(n)},Ys.endOf=function(e){var t,s;if(void 0===(e=V(e))||"millisecond"===e||!this.isValid())return this;switch(s=this._isUTC?cs:ds,e){case"year":t=s(this.year()+1,0,1)-1;break;case"quarter":t=s(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=s(this.year(),this.month()+1,1)-1;break;case"week":t=s(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=s(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=s(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=os-hs(t+(this._isUTC?0:this.utcOffset()*rs),os)-1;break;case"minute":t=this._d.valueOf(),t+=rs-hs(t,rs)-1;break;case"second":t=this._d.valueOf(),t+=ns-hs(t,ns)-1}return this._d.setTime(t),o.updateOffset(this,!0),this},Ys.format=function(e){e||(e=this.isUtc()?o.defaultFormatUtc:o.defaultFormat);var t=U(this,e);return this.localeData().postformat(t)},Ys.from=function(e,t){return this.isValid()&&(b(e)&&e.isValid()||Pt(e).isValid())?zt({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},Ys.fromNow=function(e){return this.from(Pt(),e)},Ys.to=function(e,t){return this.isValid()&&(b(e)&&e.isValid()||Pt(e).isValid())?zt({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},Ys.toNow=function(e){return this.to(Pt(),e)},Ys.get=function(e){return N(this[e=V(e)])?this[e]():this},Ys.invalidAt=function(){return _(this).overflow},Ys.isAfter=function(e,t){var s=b(e)?e:Pt(e);return!(!this.isValid()||!s.isValid())&&("millisecond"===(t=V(t)||"millisecond")?this.valueOf()>s.valueOf():s.valueOf()<this.clone().startOf(t).valueOf())},Ys.isBefore=function(e,t){var s=b(e)?e:Pt(e);return!(!this.isValid()||!s.isValid())&&("millisecond"===(t=V(t)||"millisecond")?this.valueOf()<s.valueOf():this.clone().endOf(t).valueOf()<s.valueOf())},Ys.isBetween=function(e,t,s,a){var i=b(e)?e:Pt(e),n=b(t)?t:Pt(t);return!!(this.isValid()&&i.isValid()&&n.isValid())&&("("===(a=a||"()")[0]?this.isAfter(i,s):!this.isBefore(i,s))&&(")"===a[1]?this.isBefore(n,s):!this.isAfter(n,s))},Ys.isSame=function(e,t){var s,a=b(e)?e:Pt(e);return!(!this.isValid()||!a.isValid())&&("millisecond"===(t=V(t)||"millisecond")?this.valueOf()===a.valueOf():(s=a.valueOf(),this.clone().startOf(t).valueOf()<=s&&s<=this.clone().endOf(t).valueOf()))},Ys.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},Ys.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},Ys.isValid=function(){return D(this)},Ys.lang=as,Ys.locale=ss,Ys.localeData=is,Ys.max=Tt,Ys.min=Nt,Ys.parsingFlags=function(){return y({},_(this))},Ys.set=function(e,t){if("object"==typeof e){var s,a=function(e){var t,s=[];for(t in e)d(e,t)&&s.push({unit:t,priority:z[t]});return s.sort((function(e,t){return e.priority-t.priority})),s}(e=G(e)),i=a.length;for(s=0;s<i;s++)this[a[s].unit](e[a[s].unit])}else if(N(this[e=V(e)]))return this[e](t);return this},Ys.startOf=function(e){var t,s;if(void 0===(e=V(e))||"millisecond"===e||!this.isValid())return this;switch(s=this._isUTC?cs:ds,e){case"year":t=s(this.year(),0,1);break;case"quarter":t=s(this.year(),this.month()-this.month()%3,1);break;case"month":t=s(this.year(),this.month(),1);break;case"week":t=s(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=s(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=s(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=hs(t+(this._isUTC?0:this.utcOffset()*rs),os);break;case"minute":t=this._d.valueOf(),t-=hs(t,rs);break;case"second":t=this._d.valueOf(),t-=hs(t,ns)}return this._d.setTime(t),o.updateOffset(this,!0),this},Ys.subtract=Xt,Ys.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},Ys.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},Ys.toDate=function(){return new Date(this.valueOf())},Ys.toISOString=function(e){if(!this.isValid())return null;var t=!0!==e,s=t?this.clone().utc():this;return s.year()<0||s.year()>9999?U(s,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):N(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",U(s,"Z")):U(s,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},Ys.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var e,t,s,a="moment",i="";return this.isLocal()||(a=0===this.utcOffset()?"moment.utc":"moment.parseZone",i="Z"),e="["+a+'("]',t=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",s=i+'[")]',this.format(e+t+"-MM-DD[T]HH:mm:ss.SSS"+s)},"undefined"!=typeof Symbol&&null!=Symbol.for&&(Ys[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),Ys.toJSON=function(){return this.isValid()?this.toISOString():null},Ys.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},Ys.unix=function(){return Math.floor(this.valueOf()/1e3)},Ys.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},Ys.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},Ys.eraName=function(){var e,t,s,a=this.localeData().eras();for(e=0,t=a.length;e<t;++e){if(s=this.clone().startOf("day").valueOf(),a[e].since<=s&&s<=a[e].until)return a[e].name;if(a[e].until<=s&&s<=a[e].since)return a[e].name}return""},Ys.eraNarrow=function(){var e,t,s,a=this.localeData().eras();for(e=0,t=a.length;e<t;++e){if(s=this.clone().startOf("day").valueOf(),a[e].since<=s&&s<=a[e].until)return a[e].narrow;if(a[e].until<=s&&s<=a[e].since)return a[e].narrow}return""},Ys.eraAbbr=function(){var e,t,s,a=this.localeData().eras();for(e=0,t=a.length;e<t;++e){if(s=this.clone().startOf("day").valueOf(),a[e].since<=s&&s<=a[e].until)return a[e].abbr;if(a[e].until<=s&&s<=a[e].since)return a[e].abbr}return""},Ys.eraYear=function(){var e,t,s,a,i=this.localeData().eras();for(e=0,t=i.length;e<t;++e)if(s=i[e].since<=i[e].until?1:-1,a=this.clone().startOf("day").valueOf(),i[e].since<=a&&a<=i[e].until||i[e].until<=a&&a<=i[e].since)return(this.year()-o(i[e].since).year())*s+i[e].offset;return this.year()},Ys.year=Me,Ys.isLeapYear=function(){return ve(this.year())},Ys.weekYear=function(e){return ps.call(this,e,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},Ys.isoWeekYear=function(e){return ps.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},Ys.quarter=Ys.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},Ys.month=He,Ys.daysInMonth=function(){return xe(this.year(),this.month())},Ys.week=Ys.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},Ys.isoWeek=Ys.isoWeeks=function(e){var t=je(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},Ys.weeksInYear=function(){var e=this.localeData()._week;return Ve(this.year(),e.dow,e.doy)},Ys.weeksInWeekYear=function(){var e=this.localeData()._week;return Ve(this.weekYear(),e.dow,e.doy)},Ys.isoWeeksInYear=function(){return Ve(this.year(),1,4)},Ys.isoWeeksInISOWeekYear=function(){return Ve(this.isoWeekYear(),1,4)},Ys.date=gs,Ys.day=Ys.days=function(e){if(!this.isValid())return null!=e?this:NaN;var t=Se(this,"Day");return null!=e?(e=function(e,t){return"string"!=typeof e?e:isNaN(e)?"number"==typeof(e=t.weekdaysParse(e))?e:null:parseInt(e,10)}(e,this.localeData()),this.add(e-t,"d")):t},Ys.weekday=function(e){if(!this.isValid())return null!=e?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")},Ys.isoWeekday=function(e){if(!this.isValid())return null!=e?this:NaN;if(null!=e){var t=function(e,t){return"string"==typeof e?t.weekdaysParse(e)%7||7:isNaN(e)?null:e}(e,this.localeData());return this.day(this.day()%7?t:t-7)}return this.day()||7},Ys.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},Ys.hour=Ys.hours=at,Ys.minute=Ys.minutes=_s,Ys.second=Ys.seconds=vs,Ys.millisecond=Ys.milliseconds=ks,Ys.utcOffset=function(e,t,s){var a,i=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null!=e){if("string"==typeof e){if(null===(e=Ut(le,e)))return this}else Math.abs(e)<16&&!s&&(e*=60);return!this._isUTC&&t&&(a=jt(this)),this._offset=e,this._isUTC=!0,null!=a&&this.add(a,"m"),i!==e&&(!t||this._changeInProgress?Jt(this,zt(e-i,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,o.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?i:jt(this)},Ys.utc=function(e){return this.utcOffset(0,e)},Ys.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(jt(this),"m")),this},Ys.parseZone=function(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var e=Ut(oe,this._i);null!=e?this.utcOffset(e):this.utcOffset(0,!0)}return this},Ys.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?Pt(e).utcOffset():0,(this.utcOffset()-e)%60==0)},Ys.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},Ys.isLocal=function(){return!!this.isValid()&&!this._isUTC},Ys.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},Ys.isUtc=Vt,Ys.isUTC=Vt,Ys.zoneAbbr=function(){return this._isUTC?"UTC":""},Ys.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},Ys.dates=C("dates accessor is deprecated. Use date instead.",gs),Ys.months=C("months accessor is deprecated. Use month instead",He),Ys.years=C("years accessor is deprecated. Use year instead",Me),Ys.zone=C("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",(function(e,t){return null!=e?("string"!=typeof e&&(e=-e),this.utcOffset(e,t),this):-this.utcOffset()})),Ys.isDSTShifted=C("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",(function(){if(!u(this._isDSTShifted))return this._isDSTShifted;var e,t={};return Y(t,this),(t=xt(t))._a?(e=t._isUTC?g(t._a):Pt(t._a),this._isDSTShifted=this.isValid()&&function(e,t){var s,a=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),n=0;for(s=0;s<a;s++)ye(e[s])!==ye(t[s])&&n++;return n+i}(t._a,e.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}));var bs=W.prototype;function Ss(e,t,s,a){var i=ut(),n=g().set(a,t);return i[s](n,e)}function Cs(e,t,s){if(f(e)&&(t=e,e=void 0),e=e||"",null!=t)return Ss(e,t,s,"month");var a,i=[];for(a=0;a<12;a++)i[a]=Ss(e,a,s,"month");return i}function xs(e,t,s,a){"boolean"==typeof e?(f(t)&&(s=t,t=void 0),t=t||""):(s=t=e,e=!1,f(t)&&(s=t,t=void 0),t=t||"");var i,n=ut(),r=e?n._week.dow:0,o=[];if(null!=s)return Ss(t,(s+r)%7,a,"day");for(i=0;i<7;i++)o[i]=Ss(t,(i+r)%7,a,"day");return o}bs.calendar=function(e,t,s){var a=this._calendar[e]||this._calendar.sameElse;return N(a)?a.call(t,s):a},bs.longDateFormat=function(e){var t=this._longDateFormat[e],s=this._longDateFormat[e.toUpperCase()];return t||!s?t:(this._longDateFormat[e]=s.match(I).map((function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e})).join(""),this._longDateFormat[e])},bs.invalidDate=function(){return this._invalidDate},bs.ordinal=function(e){return this._ordinal.replace("%d",e)},bs.preparse=Ms,bs.postformat=Ms,bs.relativeTime=function(e,t,s,a){var i=this._relativeTime[s];return N(i)?i(e,t,s,a):i.replace(/%d/i,e)},bs.pastFuture=function(e,t){var s=this._relativeTime[e>0?"future":"past"];return N(s)?s(t):s.replace(/%s/i,t)},bs.set=function(e){var t,s;for(s in e)d(e,s)&&(N(t=e[s])?this[s]=t:this["_"+s]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},bs.eras=function(e,t){var s,a,i,n=this._eras||ut("en")._eras;for(s=0,a=n.length;s<a;++s)switch("string"==typeof n[s].since&&(i=o(n[s].since).startOf("day"),n[s].since=i.valueOf()),typeof n[s].until){case"undefined":n[s].until=1/0;break;case"string":i=o(n[s].until).startOf("day").valueOf(),n[s].until=i.valueOf()}return n},bs.erasParse=function(e,t,s){var a,i,n,r,o,l=this.eras();for(e=e.toUpperCase(),a=0,i=l.length;a<i;++a)if(n=l[a].name.toUpperCase(),r=l[a].abbr.toUpperCase(),o=l[a].narrow.toUpperCase(),s)switch(t){case"N":case"NN":case"NNN":if(r===e)return l[a];break;case"NNNN":if(n===e)return l[a];break;case"NNNNN":if(o===e)return l[a]}else if([n,r,o].indexOf(e)>=0)return l[a]},bs.erasConvertYear=function(e,t){var s=e.since<=e.until?1:-1;return void 0===t?o(e.since).year():o(e.since).year()+(t-e.offset)*s},bs.erasAbbrRegex=function(e){return d(this,"_erasAbbrRegex")||fs.call(this),e?this._erasAbbrRegex:this._erasRegex},bs.erasNameRegex=function(e){return d(this,"_erasNameRegex")||fs.call(this),e?this._erasNameRegex:this._erasRegex},bs.erasNarrowRegex=function(e){return d(this,"_erasNarrowRegex")||fs.call(this),e?this._erasNarrowRegex:this._erasRegex},bs.months=function(e,t){return e?l(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||Ne).test(t)?"format":"standalone"][e.month()]:l(this._months)?this._months:this._months.standalone},bs.monthsShort=function(e,t){return e?l(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[Ne.test(t)?"format":"standalone"][e.month()]:l(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},bs.monthsParse=function(e,t,s){var a,i,n;if(this._monthsParseExact)return Re.call(this,e,t,s);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),a=0;a<12;a++){if(i=g([2e3,a]),s&&!this._longMonthsParse[a]&&(this._longMonthsParse[a]=new RegExp("^"+this.months(i,"").replace(".","")+"$","i"),this._shortMonthsParse[a]=new RegExp("^"+this.monthsShort(i,"").replace(".","")+"$","i")),s||this._monthsParse[a]||(n="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[a]=new RegExp(n.replace(".",""),"i")),s&&"MMMM"===t&&this._longMonthsParse[a].test(e))return a;if(s&&"MMM"===t&&this._shortMonthsParse[a].test(e))return a;if(!s&&this._monthsParse[a].test(e))return a}},bs.monthsRegex=function(e){return this._monthsParseExact?(d(this,"_monthsRegex")||Le.call(this),e?this._monthsStrictRegex:this._monthsRegex):(d(this,"_monthsRegex")||(this._monthsRegex=We),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},bs.monthsShortRegex=function(e){return this._monthsParseExact?(d(this,"_monthsRegex")||Le.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(d(this,"_monthsShortRegex")||(this._monthsShortRegex=Te),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},bs.week=function(e){return je(e,this._week.dow,this._week.doy).week},bs.firstDayOfYear=function(){return this._week.doy},bs.firstDayOfWeek=function(){return this._week.dow},bs.weekdays=function(e,t){var s=l(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"];return!0===e?Ge(s,this._week.dow):e?s[e.day()]:s},bs.weekdaysMin=function(e){return!0===e?Ge(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},bs.weekdaysShort=function(e){return!0===e?Ge(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},bs.weekdaysParse=function(e,t,s){var a,i,n;if(this._weekdaysParseExact)return Qe.call(this,e,t,s);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),a=0;a<7;a++){if(i=g([2e3,1]).day(a),s&&!this._fullWeekdaysParse[a]&&(this._fullWeekdaysParse[a]=new RegExp("^"+this.weekdays(i,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[a]=new RegExp("^"+this.weekdaysShort(i,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[a]=new RegExp("^"+this.weekdaysMin(i,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[a]||(n="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[a]=new RegExp(n.replace(".",""),"i")),s&&"dddd"===t&&this._fullWeekdaysParse[a].test(e))return a;if(s&&"ddd"===t&&this._shortWeekdaysParse[a].test(e))return a;if(s&&"dd"===t&&this._minWeekdaysParse[a].test(e))return a;if(!s&&this._weekdaysParse[a].test(e))return a}},bs.weekdaysRegex=function(e){return this._weekdaysParseExact?(d(this,"_weekdaysRegex")||Xe.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(d(this,"_weekdaysRegex")||(this._weekdaysRegex=$e),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},bs.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(d(this,"_weekdaysRegex")||Xe.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(d(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=qe),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},bs.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(d(this,"_weekdaysRegex")||Xe.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(d(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Je),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},bs.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},bs.meridiem=function(e,t,s){return e>11?s?"pm":"PM":s?"am":"AM"},dt("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===ye(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}}),o.lang=C("moment.lang is deprecated. Use moment.locale instead.",dt),o.langData=C("moment.langData is deprecated. Use moment.localeData instead.",ut);var Os=Math.abs;function Ps(e,t,s,a){var i=zt(t,s);return e._milliseconds+=a*i._milliseconds,e._days+=a*i._days,e._months+=a*i._months,e._bubble()}function Ns(e){return e<0?Math.floor(e):Math.ceil(e)}function Ts(e){return 4800*e/146097}function Ws(e){return 146097*e/4800}function Rs(e){return function(){return this.as(e)}}var Is=Rs("ms"),Hs=Rs("s"),Ls=Rs("m"),As=Rs("h"),Es=Rs("d"),Us=Rs("w"),Fs=Rs("M"),js=Rs("Q"),Vs=Rs("y"),Gs=Is;function Bs(e){return function(){return this.isValid()?this._data[e]:NaN}}var zs=Bs("milliseconds"),Zs=Bs("seconds"),$s=Bs("minutes"),qs=Bs("hours"),Js=Bs("days"),Qs=Bs("months"),Xs=Bs("years"),Ks=Math.round,ea={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function ta(e,t,s,a,i){return i.relativeTime(t||1,!!s,e,a)}var sa=Math.abs;function aa(e){return(e>0)-(e<0)||+e}function ia(){if(!this.isValid())return this.localeData().invalidDate();var e,t,s,a,i,n,r,o,l=sa(this._milliseconds)/1e3,h=sa(this._days),d=sa(this._months),c=this.asSeconds();return c?(e=pe(l/60),t=pe(e/60),l%=60,e%=60,s=pe(d/12),d%=12,a=l?l.toFixed(3).replace(/\.?0+$/,""):"",i=c<0?"-":"",n=aa(this._months)!==aa(c)?"-":"",r=aa(this._days)!==aa(c)?"-":"",o=aa(this._milliseconds)!==aa(c)?"-":"",i+"P"+(s?n+s+"Y":"")+(d?n+d+"M":"")+(h?r+h+"D":"")+(t||e||l?"T":"")+(t?o+t+"H":"")+(e?o+e+"M":"")+(l?o+a+"S":"")):"P0D"}var na=It.prototype;na.isValid=function(){return this._isValid},na.abs=function(){var e=this._data;return this._milliseconds=Os(this._milliseconds),this._days=Os(this._days),this._months=Os(this._months),e.milliseconds=Os(e.milliseconds),e.seconds=Os(e.seconds),e.minutes=Os(e.minutes),e.hours=Os(e.hours),e.months=Os(e.months),e.years=Os(e.years),this},na.add=function(e,t){return Ps(this,e,t,1)},na.subtract=function(e,t){return Ps(this,e,t,-1)},na.as=function(e){if(!this.isValid())return NaN;var t,s,a=this._milliseconds;if("month"===(e=V(e))||"quarter"===e||"year"===e)switch(t=this._days+a/864e5,s=this._months+Ts(t),e){case"month":return s;case"quarter":return s/3;case"year":return s/12}else switch(t=this._days+Math.round(Ws(this._months)),e){case"week":return t/7+a/6048e5;case"day":return t+a/864e5;case"hour":return 24*t+a/36e5;case"minute":return 1440*t+a/6e4;case"second":return 86400*t+a/1e3;case"millisecond":return Math.floor(864e5*t)+a;default:throw new Error("Unknown unit "+e)}},na.asMilliseconds=Is,na.asSeconds=Hs,na.asMinutes=Ls,na.asHours=As,na.asDays=Es,na.asWeeks=Us,na.asMonths=Fs,na.asQuarters=js,na.asYears=Vs,na.valueOf=Gs,na._bubble=function(){var e,t,s,a,i,n=this._milliseconds,r=this._days,o=this._months,l=this._data;return n>=0&&r>=0&&o>=0||n<=0&&r<=0&&o<=0||(n+=864e5*Ns(Ws(o)+r),r=0,o=0),l.milliseconds=n%1e3,e=pe(n/1e3),l.seconds=e%60,t=pe(e/60),l.minutes=t%60,s=pe(t/60),l.hours=s%24,r+=pe(s/24),o+=i=pe(Ts(r)),r-=Ns(Ws(i)),a=pe(o/12),o%=12,l.days=r,l.months=o,l.years=a,this},na.clone=function(){return zt(this)},na.get=function(e){return e=V(e),this.isValid()?this[e+"s"]():NaN},na.milliseconds=zs,na.seconds=Zs,na.minutes=$s,na.hours=qs,na.days=Js,na.weeks=function(){return pe(this.days()/7)},na.months=Qs,na.years=Xs,na.humanize=function(e,t){if(!this.isValid())return this.localeData().invalidDate();var s,a,i=!1,n=ea;return"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(i=e),"object"==typeof t&&(n=Object.assign({},ea,t),null!=t.s&&null==t.ss&&(n.ss=t.s-1)),a=function(e,t,s,a){var i=zt(e).abs(),n=Ks(i.as("s")),r=Ks(i.as("m")),o=Ks(i.as("h")),l=Ks(i.as("d")),h=Ks(i.as("M")),d=Ks(i.as("w")),c=Ks(i.as("y")),u=n<=s.ss&&["s",n]||n<s.s&&["ss",n]||r<=1&&["m"]||r<s.m&&["mm",r]||o<=1&&["h"]||o<s.h&&["hh",o]||l<=1&&["d"]||l<s.d&&["dd",l];return null!=s.w&&(u=u||d<=1&&["w"]||d<s.w&&["ww",d]),(u=u||h<=1&&["M"]||h<s.M&&["MM",h]||c<=1&&["y"]||["yy",c])[2]=t,u[3]=+e>0,u[4]=a,ta.apply(null,u)}(this,!i,n,s=this.localeData()),i&&(a=s.pastFuture(+this,a)),s.postformat(a)},na.toISOString=ia,na.toString=ia,na.toJSON=ia,na.locale=ss,na.localeData=is,na.toIsoString=C("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",ia),na.lang=as,E("X",0,0,"unix"),E("x",0,0,"valueOf"),ue("x",re),ue("X",/[+-]?\d+(\.\d{1,3})?/),_e("X",(function(e,t,s){s._d=new Date(1e3*parseFloat(e))})),_e("x",(function(e,t,s){s._d=new Date(ye(e))})),
//! moment.js
o.version="2.30.1",n=Pt,o.fn=Ys,o.min=function(){return Wt("isBefore",[].slice.call(arguments,0))},o.max=function(){return Wt("isAfter",[].slice.call(arguments,0))},o.now=function(){return Date.now?Date.now():+new Date},o.utc=g,o.unix=function(e){return Pt(1e3*e)},o.months=function(e,t){return Cs(e,t,"months")},o.isDate=m,o.locale=dt,o.invalid=k,o.duration=zt,o.isMoment=b,o.weekdays=function(e,t,s){return xs(e,t,s,"weekdays")},o.parseZone=function(){return Pt.apply(null,arguments).parseZone()},o.localeData=ut,o.isDuration=Ht,o.monthsShort=function(e,t){return Cs(e,t,"monthsShort")},o.weekdaysMin=function(e,t,s){return xs(e,t,s,"weekdaysMin")},o.defineLocale=ct,o.updateLocale=function(e,t){if(null!=t){var s,a,i=it;null!=nt[e]&&null!=nt[e].parentLocale?nt[e].set(T(nt[e]._config,t)):(null!=(a=ht(e))&&(i=a._config),t=T(i,t),null==a&&(t.abbr=e),(s=new W(t)).parentLocale=nt[e],nt[e]=s),dt(e)}else null!=nt[e]&&(null!=nt[e].parentLocale?(nt[e]=nt[e].parentLocale,e===dt()&&dt(e)):null!=nt[e]&&delete nt[e]);return nt[e]},o.locales=function(){return x(nt)},o.weekdaysShort=function(e,t,s){return xs(e,t,s,"weekdaysShort")},o.normalizeUnits=V,o.relativeTimeRounding=function(e){return void 0===e?Ks:"function"==typeof e&&(Ks=e,!0)},o.relativeTimeThreshold=function(e,t){return void 0!==ea[e]&&(void 0===t?ea[e]:(ea[e]=t,"s"===e&&(ea.ss=t-1),!0))},o.calendarFormat=function(e,t){var s=e.diff(t,"days",!0);return s<-6?"sameElse":s<-1?"lastWeek":s<0?"lastDay":s<1?"sameDay":s<2?"nextDay":s<7?"nextWeek":"sameElse"},o.prototype=Ys,o.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"};const ra=Object.freeze(Object.defineProperty({__proto__:null,default:o},Symbol.toStringTag,{value:"Module"})),oa=t(ra);
/**
    * @version: 3.1
    * @author: Dan Grossman http://www.dangrossman.info/
    * @copyright: Copyright (c) 2012-2019 Dan Grossman. All rights reserved.
    * @license: Licensed under the MIT license. See http://www.opensource.org/licenses/mit-license.php
    * @website: http://www.daterangepicker.com/
    */
var la;la={exports:{}},function(e,t){if(la.exports){var s="undefined"!=typeof window?window.jQuery:void 0;s||(s=a()).fn||(s.fn={});var i="undefined"!=typeof window&&void 0!==window.moment?window.moment:oa;la.exports=t(i,s)}else e.daterangepicker=t(e.moment,e.jQuery)}(s,(function(e,t){var s=function(s,a,i){if(this.parentEl="body",this.element=t(s),this.startDate=e().startOf("day"),this.endDate=e().endOf("day"),this.minDate=!1,this.maxDate=!1,this.maxSpan=!1,this.autoApply=!1,this.singleDatePicker=!1,this.showDropdowns=!1,this.minYear=e().subtract(100,"year").format("YYYY"),this.maxYear=e().add(100,"year").format("YYYY"),this.showWeekNumbers=!1,this.showISOWeekNumbers=!1,this.showCustomRangeLabel=!0,this.timePicker=!1,this.timePicker24Hour=!1,this.timePickerIncrement=1,this.timePickerSeconds=!1,this.linkedCalendars=!0,this.autoUpdateInput=!0,this.alwaysShowCalendars=!1,this.ranges={},this.opens="right",this.element.hasClass("pull-right")&&(this.opens="left"),this.drops="down",this.element.hasClass("dropup")&&(this.drops="up"),this.buttonClasses="btn btn-sm",this.applyButtonClasses="btn-primary",this.cancelButtonClasses="btn-default",this.locale={direction:"ltr",format:e.localeData().longDateFormat("L"),separator:" - ",applyLabel:"Apply",cancelLabel:"Cancel",weekLabel:"W",customRangeLabel:"Custom Range",daysOfWeek:e.weekdaysMin(),monthNames:e.monthsShort(),firstDay:e.localeData().firstDayOfWeek()},this.callback=function(){},this.isShowing=!1,this.leftCalendar={},this.rightCalendar={},"object"==typeof a&&null!==a||(a={}),"string"==typeof(a=t.extend(this.element.data(),a)).template||a.template instanceof t||(a.template='<div class="daterangepicker"><div class="ranges"></div><div class="drp-calendar left"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-calendar right"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-buttons"><span class="drp-selected"></span><button class="cancelBtn" type="button"></button><button class="applyBtn" disabled="disabled" type="button"></button> </div></div>'),this.parentEl=a.parentEl&&t(a.parentEl).length?t(a.parentEl):t(this.parentEl),this.container=t(a.template).appendTo(this.parentEl),"object"==typeof a.locale&&("string"==typeof a.locale.direction&&(this.locale.direction=a.locale.direction),"string"==typeof a.locale.format&&(this.locale.format=a.locale.format),"string"==typeof a.locale.separator&&(this.locale.separator=a.locale.separator),"object"==typeof a.locale.daysOfWeek&&(this.locale.daysOfWeek=a.locale.daysOfWeek.slice()),"object"==typeof a.locale.monthNames&&(this.locale.monthNames=a.locale.monthNames.slice()),"number"==typeof a.locale.firstDay&&(this.locale.firstDay=a.locale.firstDay),"string"==typeof a.locale.applyLabel&&(this.locale.applyLabel=a.locale.applyLabel),"string"==typeof a.locale.cancelLabel&&(this.locale.cancelLabel=a.locale.cancelLabel),"string"==typeof a.locale.weekLabel&&(this.locale.weekLabel=a.locale.weekLabel),"string"==typeof a.locale.customRangeLabel)){(u=document.createElement("textarea")).innerHTML=a.locale.customRangeLabel;var n=u.value;this.locale.customRangeLabel=n}if(this.container.addClass(this.locale.direction),"string"==typeof a.startDate&&(this.startDate=e(a.startDate,this.locale.format)),"string"==typeof a.endDate&&(this.endDate=e(a.endDate,this.locale.format)),"string"==typeof a.minDate&&(this.minDate=e(a.minDate,this.locale.format)),"string"==typeof a.maxDate&&(this.maxDate=e(a.maxDate,this.locale.format)),"object"==typeof a.startDate&&(this.startDate=e(a.startDate)),"object"==typeof a.endDate&&(this.endDate=e(a.endDate)),"object"==typeof a.minDate&&(this.minDate=e(a.minDate)),"object"==typeof a.maxDate&&(this.maxDate=e(a.maxDate)),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),"string"==typeof a.applyButtonClasses&&(this.applyButtonClasses=a.applyButtonClasses),"string"==typeof a.applyClass&&(this.applyButtonClasses=a.applyClass),"string"==typeof a.cancelButtonClasses&&(this.cancelButtonClasses=a.cancelButtonClasses),"string"==typeof a.cancelClass&&(this.cancelButtonClasses=a.cancelClass),"object"==typeof a.maxSpan&&(this.maxSpan=a.maxSpan),"object"==typeof a.dateLimit&&(this.maxSpan=a.dateLimit),"string"==typeof a.opens&&(this.opens=a.opens),"string"==typeof a.drops&&(this.drops=a.drops),"boolean"==typeof a.showWeekNumbers&&(this.showWeekNumbers=a.showWeekNumbers),"boolean"==typeof a.showISOWeekNumbers&&(this.showISOWeekNumbers=a.showISOWeekNumbers),"string"==typeof a.buttonClasses&&(this.buttonClasses=a.buttonClasses),"object"==typeof a.buttonClasses&&(this.buttonClasses=a.buttonClasses.join(" ")),"boolean"==typeof a.showDropdowns&&(this.showDropdowns=a.showDropdowns),"number"==typeof a.minYear&&(this.minYear=a.minYear),"number"==typeof a.maxYear&&(this.maxYear=a.maxYear),"boolean"==typeof a.showCustomRangeLabel&&(this.showCustomRangeLabel=a.showCustomRangeLabel),"boolean"==typeof a.singleDatePicker&&(this.singleDatePicker=a.singleDatePicker,this.singleDatePicker&&(this.endDate=this.startDate.clone())),"boolean"==typeof a.timePicker&&(this.timePicker=a.timePicker),"boolean"==typeof a.timePickerSeconds&&(this.timePickerSeconds=a.timePickerSeconds),"number"==typeof a.timePickerIncrement&&(this.timePickerIncrement=a.timePickerIncrement),"boolean"==typeof a.timePicker24Hour&&(this.timePicker24Hour=a.timePicker24Hour),"boolean"==typeof a.autoApply&&(this.autoApply=a.autoApply),"boolean"==typeof a.autoUpdateInput&&(this.autoUpdateInput=a.autoUpdateInput),"boolean"==typeof a.linkedCalendars&&(this.linkedCalendars=a.linkedCalendars),"function"==typeof a.isInvalidDate&&(this.isInvalidDate=a.isInvalidDate),"function"==typeof a.isCustomDate&&(this.isCustomDate=a.isCustomDate),"boolean"==typeof a.alwaysShowCalendars&&(this.alwaysShowCalendars=a.alwaysShowCalendars),0!=this.locale.firstDay)for(var r=this.locale.firstDay;r>0;)this.locale.daysOfWeek.push(this.locale.daysOfWeek.shift()),r--;var o,l,h;if(void 0===a.startDate&&void 0===a.endDate&&t(this.element).is(":text")){var d=t(this.element).val(),c=d.split(this.locale.separator);o=l=null,2==c.length?(o=e(c[0],this.locale.format),l=e(c[1],this.locale.format)):this.singleDatePicker&&""!==d&&(o=e(d,this.locale.format),l=e(d,this.locale.format)),null!==o&&null!==l&&(this.setStartDate(o),this.setEndDate(l))}if("object"==typeof a.ranges){for(h in a.ranges){o="string"==typeof a.ranges[h][0]?e(a.ranges[h][0],this.locale.format):e(a.ranges[h][0]),l="string"==typeof a.ranges[h][1]?e(a.ranges[h][1],this.locale.format):e(a.ranges[h][1]),this.minDate&&o.isBefore(this.minDate)&&(o=this.minDate.clone());var u,f=this.maxDate;this.maxSpan&&f&&o.clone().add(this.maxSpan).isAfter(f)&&(f=o.clone().add(this.maxSpan)),f&&l.isAfter(f)&&(l=f.clone()),this.minDate&&l.isBefore(this.minDate,this.timepicker?"minute":"day")||f&&o.isAfter(f,this.timepicker?"minute":"day")||((u=document.createElement("textarea")).innerHTML=h,n=u.value,this.ranges[n]=[o,l])}var m="<ul>";for(h in this.ranges)m+='<li data-range-key="'+h+'">'+h+"</li>";this.showCustomRangeLabel&&(m+='<li data-range-key="'+this.locale.customRangeLabel+'">'+this.locale.customRangeLabel+"</li>"),m+="</ul>",this.container.find(".ranges").prepend(m)}"function"==typeof i&&(this.callback=i),this.timePicker||(this.startDate=this.startDate.startOf("day"),this.endDate=this.endDate.endOf("day"),this.container.find(".calendar-time").hide()),this.timePicker&&this.autoApply&&(this.autoApply=!1),this.autoApply&&this.container.addClass("auto-apply"),"object"==typeof a.ranges&&this.container.addClass("show-ranges"),this.singleDatePicker&&(this.container.addClass("single"),this.container.find(".drp-calendar.left").addClass("single"),this.container.find(".drp-calendar.left").show(),this.container.find(".drp-calendar.right").hide(),!this.timePicker&&this.autoApply&&this.container.addClass("auto-apply")),(void 0===a.ranges&&!this.singleDatePicker||this.alwaysShowCalendars)&&this.container.addClass("show-calendar"),this.container.addClass("opens"+this.opens),this.container.find(".applyBtn, .cancelBtn").addClass(this.buttonClasses),this.applyButtonClasses.length&&this.container.find(".applyBtn").addClass(this.applyButtonClasses),this.cancelButtonClasses.length&&this.container.find(".cancelBtn").addClass(this.cancelButtonClasses),this.container.find(".applyBtn").html(this.locale.applyLabel),this.container.find(".cancelBtn").html(this.locale.cancelLabel),this.container.find(".drp-calendar").on("click.daterangepicker",".prev",t.proxy(this.clickPrev,this)).on("click.daterangepicker",".next",t.proxy(this.clickNext,this)).on("mousedown.daterangepicker","td.available",t.proxy(this.clickDate,this)).on("mouseenter.daterangepicker","td.available",t.proxy(this.hoverDate,this)).on("change.daterangepicker","select.yearselect",t.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.monthselect",t.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.hourselect,select.minuteselect,select.secondselect,select.ampmselect",t.proxy(this.timeChanged,this)),this.container.find(".ranges").on("click.daterangepicker","li",t.proxy(this.clickRange,this)),this.container.find(".drp-buttons").on("click.daterangepicker","button.applyBtn",t.proxy(this.clickApply,this)).on("click.daterangepicker","button.cancelBtn",t.proxy(this.clickCancel,this)),this.element.is("input")||this.element.is("button")?this.element.on({"click.daterangepicker":t.proxy(this.show,this),"focus.daterangepicker":t.proxy(this.show,this),"keyup.daterangepicker":t.proxy(this.elementChanged,this),"keydown.daterangepicker":t.proxy(this.keydown,this)}):(this.element.on("click.daterangepicker",t.proxy(this.toggle,this)),this.element.on("keydown.daterangepicker",t.proxy(this.toggle,this))),this.updateElement()};return s.prototype={constructor:s,setStartDate:function(t){"string"==typeof t&&(this.startDate=e(t,this.locale.format)),"object"==typeof t&&(this.startDate=e(t)),this.timePicker||(this.startDate=this.startDate.startOf("day")),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.maxDate&&this.startDate.isAfter(this.maxDate)&&(this.startDate=this.maxDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.floor(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.isShowing||this.updateElement(),this.updateMonthsInView()},setEndDate:function(t){"string"==typeof t&&(this.endDate=e(t,this.locale.format)),"object"==typeof t&&(this.endDate=e(t)),this.timePicker||(this.endDate=this.endDate.endOf("day")),this.timePicker&&this.timePickerIncrement&&this.endDate.minute(Math.round(this.endDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.endDate.isBefore(this.startDate)&&(this.endDate=this.startDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),this.maxSpan&&this.startDate.clone().add(this.maxSpan).isBefore(this.endDate)&&(this.endDate=this.startDate.clone().add(this.maxSpan)),this.previousRightTime=this.endDate.clone(),this.container.find(".drp-selected").html(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.isShowing||this.updateElement(),this.updateMonthsInView()},isInvalidDate:function(){return!1},isCustomDate:function(){return!1},updateView:function(){this.timePicker&&(this.renderTimePicker("left"),this.renderTimePicker("right"),this.endDate?this.container.find(".right .calendar-time select").prop("disabled",!1).removeClass("disabled"):this.container.find(".right .calendar-time select").prop("disabled",!0).addClass("disabled")),this.endDate&&this.container.find(".drp-selected").html(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.updateMonthsInView(),this.updateCalendars(),this.updateFormInputs()},updateMonthsInView:function(){if(this.endDate){if(!this.singleDatePicker&&this.leftCalendar.month&&this.rightCalendar.month&&(this.startDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.startDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM"))&&(this.endDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.endDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM")))return;this.leftCalendar.month=this.startDate.clone().date(2),this.linkedCalendars||this.endDate.month()==this.startDate.month()&&this.endDate.year()==this.startDate.year()?this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"):this.rightCalendar.month=this.endDate.clone().date(2)}else this.leftCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&this.rightCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&(this.leftCalendar.month=this.startDate.clone().date(2),this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"));this.maxDate&&this.linkedCalendars&&!this.singleDatePicker&&this.rightCalendar.month>this.maxDate&&(this.rightCalendar.month=this.maxDate.clone().date(2),this.leftCalendar.month=this.maxDate.clone().date(2).subtract(1,"month"))},updateCalendars:function(){var e,t,s,a;this.timePicker&&(this.endDate?(e=parseInt(this.container.find(".left .hourselect").val(),10),t=parseInt(this.container.find(".left .minuteselect").val(),10),isNaN(t)&&(t=parseInt(this.container.find(".left .minuteselect option:last").val(),10)),s=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0,this.timePicker24Hour||("PM"===(a=this.container.find(".left .ampmselect").val())&&e<12&&(e+=12),"AM"===a&&12===e&&(e=0))):(e=parseInt(this.container.find(".right .hourselect").val(),10),t=parseInt(this.container.find(".right .minuteselect").val(),10),isNaN(t)&&(t=parseInt(this.container.find(".right .minuteselect option:last").val(),10)),s=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0,this.timePicker24Hour||("PM"===(a=this.container.find(".right .ampmselect").val())&&e<12&&(e+=12),"AM"===a&&12===e&&(e=0))),this.leftCalendar.month.hour(e).minute(t).second(s),this.rightCalendar.month.hour(e).minute(t).second(s)),this.renderCalendar("left"),this.renderCalendar("right"),this.container.find(".ranges li").removeClass("active"),null!=this.endDate&&this.calculateChosenLabel()},renderCalendar:function(s){var a,i=(a="left"==s?this.leftCalendar:this.rightCalendar).month.month(),n=a.month.year(),r=a.month.hour(),o=a.month.minute(),l=a.month.second(),h=e([n,i]).daysInMonth(),d=e([n,i,1]),c=e([n,i,h]),u=e(d).subtract(1,"month").month(),f=e(d).subtract(1,"month").year(),m=e([f,u]).daysInMonth(),p=d.day();(a=[]).firstDay=d,a.lastDay=c;for(var y=0;y<6;y++)a[y]=[];var g=m-p+this.locale.firstDay+1;g>m&&(g-=7),p==this.locale.firstDay&&(g=m-6);for(var _=e([f,u,g,12,o,l]),D=(y=0,0),k=0;y<42;y++,D++,_=e(_).add(24,"hour"))y>0&&D%7==0&&(D=0,k++),a[k][D]=_.clone().hour(r).minute(o).second(l),_.hour(12),this.minDate&&a[k][D].format("YYYY-MM-DD")==this.minDate.format("YYYY-MM-DD")&&a[k][D].isBefore(this.minDate)&&"left"==s&&(a[k][D]=this.minDate.clone()),this.maxDate&&a[k][D].format("YYYY-MM-DD")==this.maxDate.format("YYYY-MM-DD")&&a[k][D].isAfter(this.maxDate)&&"right"==s&&(a[k][D]=this.maxDate.clone());"left"==s?this.leftCalendar.calendar=a:this.rightCalendar.calendar=a;var v="left"==s?this.minDate:this.startDate,w=this.maxDate;"left"==s?this.startDate:this.endDate,this.locale.direction;var Y='<table class="table-condensed">';Y+="<thead>",Y+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(Y+="<th></th>"),v&&!v.isBefore(a.firstDay)||this.linkedCalendars&&"left"!=s?Y+="<th></th>":Y+='<th class="prev available"><span></span></th>';var M=this.locale.monthNames[a[1][1].month()]+a[1][1].format(" YYYY");if(this.showDropdowns){for(var b=a[1][1].month(),S=a[1][1].year(),C=w&&w.year()||this.maxYear,x=v&&v.year()||this.minYear,O=S==x,P=S==C,N='<select class="monthselect">',T=0;T<12;T++)(!O||v&&T>=v.month())&&(!P||w&&T<=w.month())?N+="<option value='"+T+"'"+(T===b?" selected='selected'":"")+">"+this.locale.monthNames[T]+"</option>":N+="<option value='"+T+"'"+(T===b?" selected='selected'":"")+" disabled='disabled'>"+this.locale.monthNames[T]+"</option>";N+="</select>";for(var W='<select class="yearselect">',R=x;R<=C;R++)W+='<option value="'+R+'"'+(R===S?' selected="selected"':"")+">"+R+"</option>";M=N+(W+="</select>")}if(Y+='<th colspan="5" class="month">'+M+"</th>",w&&!w.isAfter(a.lastDay)||this.linkedCalendars&&"right"!=s&&!this.singleDatePicker?Y+="<th></th>":Y+='<th class="next available"><span></span></th>',Y+="</tr>",Y+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(Y+='<th class="week">'+this.locale.weekLabel+"</th>"),t.each(this.locale.daysOfWeek,(function(e,t){Y+="<th>"+t+"</th>"})),Y+="</tr>",Y+="</thead>",Y+="<tbody>",null==this.endDate&&this.maxSpan){var I=this.startDate.clone().add(this.maxSpan).endOf("day");w&&!I.isBefore(w)||(w=I)}for(k=0;k<6;k++){for(Y+="<tr>",this.showWeekNumbers?Y+='<td class="week">'+a[k][0].week()+"</td>":this.showISOWeekNumbers&&(Y+='<td class="week">'+a[k][0].isoWeek()+"</td>"),D=0;D<7;D++){var H=[];a[k][D].isSame(new Date,"day")&&H.push("today"),a[k][D].isoWeekday()>5&&H.push("weekend"),a[k][D].month()!=a[1][1].month()&&H.push("off","ends"),this.minDate&&a[k][D].isBefore(this.minDate,"day")&&H.push("off","disabled"),w&&a[k][D].isAfter(w,"day")&&H.push("off","disabled"),this.isInvalidDate(a[k][D])&&H.push("off","disabled"),a[k][D].format("YYYY-MM-DD")==this.startDate.format("YYYY-MM-DD")&&H.push("active","start-date"),null!=this.endDate&&a[k][D].format("YYYY-MM-DD")==this.endDate.format("YYYY-MM-DD")&&H.push("active","end-date"),null!=this.endDate&&a[k][D]>this.startDate&&a[k][D]<this.endDate&&H.push("in-range");var L=this.isCustomDate(a[k][D]);!1!==L&&("string"==typeof L?H.push(L):Array.prototype.push.apply(H,L));var A="",E=!1;for(y=0;y<H.length;y++)A+=H[y]+" ","disabled"==H[y]&&(E=!0);E||(A+="available"),Y+='<td class="'+A.replace(/^\s+|\s+$/g,"")+'" data-title="r'+k+"c"+D+'">'+a[k][D].date()+"</td>"}Y+="</tr>"}Y+="</tbody>",Y+="</table>",this.container.find(".drp-calendar."+s+" .calendar-table").html(Y)},renderTimePicker:function(e){if("right"!=e||this.endDate){var t,s,a,i=this.maxDate;if(!this.maxSpan||this.maxDate&&!this.startDate.clone().add(this.maxSpan).isBefore(this.maxDate)||(i=this.startDate.clone().add(this.maxSpan)),"left"==e)s=this.startDate.clone(),a=this.minDate;else if("right"==e){s=this.endDate.clone(),a=this.startDate;var n=this.container.find(".drp-calendar.right .calendar-time");if(""!=n.html()&&(s.hour(isNaN(s.hour())?n.find(".hourselect option:selected").val():s.hour()),s.minute(isNaN(s.minute())?n.find(".minuteselect option:selected").val():s.minute()),s.second(isNaN(s.second())?n.find(".secondselect option:selected").val():s.second()),!this.timePicker24Hour)){var r=n.find(".ampmselect option:selected").val();"PM"===r&&s.hour()<12&&s.hour(s.hour()+12),"AM"===r&&12===s.hour()&&s.hour(0)}s.isBefore(this.startDate)&&(s=this.startDate.clone()),i&&s.isAfter(i)&&(s=i.clone())}t='<select class="hourselect">';for(var o=this.timePicker24Hour?0:1,l=this.timePicker24Hour?23:12,h=o;h<=l;h++){var d=h;this.timePicker24Hour||(d=s.hour()>=12?12==h?12:h+12:12==h?0:h);var c=s.clone().hour(d),u=!1;a&&c.minute(59).isBefore(a)&&(u=!0),i&&c.minute(0).isAfter(i)&&(u=!0),d!=s.hour()||u?t+=u?'<option value="'+h+'" disabled="disabled" class="disabled">'+h+"</option>":'<option value="'+h+'">'+h+"</option>":t+='<option value="'+h+'" selected="selected">'+h+"</option>"}for(t+="</select> ",t+=': <select class="minuteselect">',h=0;h<60;h+=this.timePickerIncrement){var f=h<10?"0"+h:h;c=s.clone().minute(h),u=!1,a&&c.second(59).isBefore(a)&&(u=!0),i&&c.second(0).isAfter(i)&&(u=!0),s.minute()!=h||u?t+=u?'<option value="'+h+'" disabled="disabled" class="disabled">'+f+"</option>":'<option value="'+h+'">'+f+"</option>":t+='<option value="'+h+'" selected="selected">'+f+"</option>"}if(t+="</select> ",this.timePickerSeconds){for(t+=': <select class="secondselect">',h=0;h<60;h++)f=h<10?"0"+h:h,c=s.clone().second(h),u=!1,a&&c.isBefore(a)&&(u=!0),i&&c.isAfter(i)&&(u=!0),s.second()!=h||u?t+=u?'<option value="'+h+'" disabled="disabled" class="disabled">'+f+"</option>":'<option value="'+h+'">'+f+"</option>":t+='<option value="'+h+'" selected="selected">'+f+"</option>";t+="</select> "}if(!this.timePicker24Hour){t+='<select class="ampmselect">';var m="",p="";a&&s.clone().hour(12).minute(0).second(0).isBefore(a)&&(m=' disabled="disabled" class="disabled"'),i&&s.clone().hour(0).minute(0).second(0).isAfter(i)&&(p=' disabled="disabled" class="disabled"'),s.hour()>=12?t+='<option value="AM"'+m+'>AM</option><option value="PM" selected="selected"'+p+">PM</option>":t+='<option value="AM" selected="selected"'+m+'>AM</option><option value="PM"'+p+">PM</option>",t+="</select>"}this.container.find(".drp-calendar."+e+" .calendar-time").html(t)}},updateFormInputs:function(){this.singleDatePicker||this.endDate&&(this.startDate.isBefore(this.endDate)||this.startDate.isSame(this.endDate))?this.container.find("button.applyBtn").prop("disabled",!1):this.container.find("button.applyBtn").prop("disabled",!0)},move:function(){var e,s={top:0,left:0},a=this.drops,i=t(window).width();switch(this.parentEl.is("body")||(s={top:this.parentEl.offset().top-this.parentEl.scrollTop(),left:this.parentEl.offset().left-this.parentEl.scrollLeft()},i=this.parentEl[0].clientWidth+this.parentEl.offset().left),a){case"auto":(e=this.element.offset().top+this.element.outerHeight()-s.top)+this.container.outerHeight()>=this.parentEl[0].scrollHeight&&(e=this.element.offset().top-this.container.outerHeight()-s.top,a="up");break;case"up":e=this.element.offset().top-this.container.outerHeight()-s.top;break;default:e=this.element.offset().top+this.element.outerHeight()-s.top}this.container.css({top:0,left:0,right:"auto"});var n=this.container.outerWidth();if(this.container.toggleClass("drop-up","up"==a),"left"==this.opens){var r=i-this.element.offset().left-this.element.outerWidth();n+r>t(window).width()?this.container.css({top:e,right:"auto",left:9}):this.container.css({top:e,right:r,left:"auto"})}else if("center"==this.opens)(o=this.element.offset().left-s.left+this.element.outerWidth()/2-n/2)<0?this.container.css({top:e,right:"auto",left:9}):o+n>t(window).width()?this.container.css({top:e,left:"auto",right:0}):this.container.css({top:e,left:o,right:"auto"});else{var o;(o=this.element.offset().left-s.left)+n>t(window).width()?this.container.css({top:e,left:"auto",right:0}):this.container.css({top:e,left:o,right:"auto"})}},show:function(e){this.isShowing||(this._outsideClickProxy=t.proxy((function(e){this.outsideClick(e)}),this),t(document).on("mousedown.daterangepicker",this._outsideClickProxy).on("touchend.daterangepicker",this._outsideClickProxy).on("click.daterangepicker","[data-toggle=dropdown]",this._outsideClickProxy).on("focusin.daterangepicker",this._outsideClickProxy),t(window).on("resize.daterangepicker",t.proxy((function(e){this.move(e)}),this)),this.oldStartDate=this.startDate.clone(),this.oldEndDate=this.endDate.clone(),this.previousRightTime=this.endDate.clone(),this.updateView(),this.container.show(),this.move(),this.element.trigger("show.daterangepicker",this),this.isShowing=!0)},hide:function(e){this.isShowing&&(this.endDate||(this.startDate=this.oldStartDate.clone(),this.endDate=this.oldEndDate.clone()),this.startDate.isSame(this.oldStartDate)&&this.endDate.isSame(this.oldEndDate)||this.callback(this.startDate.clone(),this.endDate.clone(),this.chosenLabel),this.updateElement(),t(document).off(".daterangepicker"),t(window).off(".daterangepicker"),this.container.hide(),this.element.trigger("hide.daterangepicker",this),this.isShowing=!1)},toggle:function(e){this.isShowing?this.hide():this.show()},outsideClick:function(e){var s=t(e.target);"focusin"==e.type||s.closest(this.element).length||s.closest(this.container).length||s.closest(".calendar-table").length||(this.hide(),this.element.trigger("outsideClick.daterangepicker",this))},showCalendars:function(){this.container.addClass("show-calendar"),this.move(),this.element.trigger("showCalendar.daterangepicker",this)},hideCalendars:function(){this.container.removeClass("show-calendar"),this.element.trigger("hideCalendar.daterangepicker",this)},clickRange:function(e){var t=e.target.getAttribute("data-range-key");if(this.chosenLabel=t,t==this.locale.customRangeLabel)this.showCalendars();else{var s=this.ranges[t];this.startDate=s[0],this.endDate=s[1],this.timePicker||(this.startDate.startOf("day"),this.endDate.endOf("day")),this.alwaysShowCalendars||this.hideCalendars(),this.clickApply()}},clickPrev:function(e){t(e.target).parents(".drp-calendar").hasClass("left")?(this.leftCalendar.month.subtract(1,"month"),this.linkedCalendars&&this.rightCalendar.month.subtract(1,"month")):this.rightCalendar.month.subtract(1,"month"),this.updateCalendars()},clickNext:function(e){t(e.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar.month.add(1,"month"):(this.rightCalendar.month.add(1,"month"),this.linkedCalendars&&this.leftCalendar.month.add(1,"month")),this.updateCalendars()},hoverDate:function(e){if(t(e.target).hasClass("available")){var s=t(e.target).attr("data-title"),a=s.substr(1,1),i=s.substr(3,1),n=t(e.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar.calendar[a][i]:this.rightCalendar.calendar[a][i],r=this.leftCalendar,o=this.rightCalendar,l=this.startDate;this.endDate||this.container.find(".drp-calendar tbody td").each((function(e,s){if(!t(s).hasClass("week")){var a=t(s).attr("data-title"),i=a.substr(1,1),h=a.substr(3,1),d=t(s).parents(".drp-calendar").hasClass("left")?r.calendar[i][h]:o.calendar[i][h];d.isAfter(l)&&d.isBefore(n)||d.isSame(n,"day")?t(s).addClass("in-range"):t(s).removeClass("in-range")}}))}},clickDate:function(e){if(t(e.target).hasClass("available")){var s=t(e.target).attr("data-title"),a=s.substr(1,1),i=s.substr(3,1),n=t(e.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar.calendar[a][i]:this.rightCalendar.calendar[a][i];if(this.endDate||n.isBefore(this.startDate,"day")){if(this.timePicker){var r=parseInt(this.container.find(".left .hourselect").val(),10);this.timePicker24Hour||("PM"===(h=this.container.find(".left .ampmselect").val())&&r<12&&(r+=12),"AM"===h&&12===r&&(r=0));var o=parseInt(this.container.find(".left .minuteselect").val(),10);isNaN(o)&&(o=parseInt(this.container.find(".left .minuteselect option:last").val(),10));var l=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0;n=n.clone().hour(r).minute(o).second(l)}this.endDate=null,this.setStartDate(n.clone())}else if(!this.endDate&&n.isBefore(this.startDate))this.setEndDate(this.startDate.clone());else{var h;this.timePicker&&(r=parseInt(this.container.find(".right .hourselect").val(),10),this.timePicker24Hour||("PM"===(h=this.container.find(".right .ampmselect").val())&&r<12&&(r+=12),"AM"===h&&12===r&&(r=0)),o=parseInt(this.container.find(".right .minuteselect").val(),10),isNaN(o)&&(o=parseInt(this.container.find(".right .minuteselect option:last").val(),10)),l=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0,n=n.clone().hour(r).minute(o).second(l)),this.setEndDate(n.clone()),this.autoApply&&(this.calculateChosenLabel(),this.clickApply())}this.singleDatePicker&&(this.setEndDate(this.startDate),!this.timePicker&&this.autoApply&&this.clickApply()),this.updateView(),e.stopPropagation()}},calculateChosenLabel:function(){var e=!0,t=0;for(var s in this.ranges){if(this.timePicker){var a=this.timePickerSeconds?"YYYY-MM-DD HH:mm:ss":"YYYY-MM-DD HH:mm";if(this.startDate.format(a)==this.ranges[s][0].format(a)&&this.endDate.format(a)==this.ranges[s][1].format(a)){e=!1,this.chosenLabel=this.container.find(".ranges li:eq("+t+")").addClass("active").attr("data-range-key");break}}else if(this.startDate.format("YYYY-MM-DD")==this.ranges[s][0].format("YYYY-MM-DD")&&this.endDate.format("YYYY-MM-DD")==this.ranges[s][1].format("YYYY-MM-DD")){e=!1,this.chosenLabel=this.container.find(".ranges li:eq("+t+")").addClass("active").attr("data-range-key");break}t++}e&&(this.showCustomRangeLabel?this.chosenLabel=this.container.find(".ranges li:last").addClass("active").attr("data-range-key"):this.chosenLabel=null,this.showCalendars())},clickApply:function(e){this.hide(),this.element.trigger("apply.daterangepicker",this)},clickCancel:function(e){this.startDate=this.oldStartDate,this.endDate=this.oldEndDate,this.hide(),this.element.trigger("cancel.daterangepicker",this)},monthOrYearChanged:function(e){var s=t(e.target).closest(".drp-calendar").hasClass("left"),a=s?"left":"right",i=this.container.find(".drp-calendar."+a),n=parseInt(i.find(".monthselect").val(),10),r=i.find(".yearselect").val();s||(r<this.startDate.year()||r==this.startDate.year()&&n<this.startDate.month())&&(n=this.startDate.month(),r=this.startDate.year()),this.minDate&&(r<this.minDate.year()||r==this.minDate.year()&&n<this.minDate.month())&&(n=this.minDate.month(),r=this.minDate.year()),this.maxDate&&(r>this.maxDate.year()||r==this.maxDate.year()&&n>this.maxDate.month())&&(n=this.maxDate.month(),r=this.maxDate.year()),s?(this.leftCalendar.month.month(n).year(r),this.linkedCalendars&&(this.rightCalendar.month=this.leftCalendar.month.clone().add(1,"month"))):(this.rightCalendar.month.month(n).year(r),this.linkedCalendars&&(this.leftCalendar.month=this.rightCalendar.month.clone().subtract(1,"month"))),this.updateCalendars()},timeChanged:function(e){var s=t(e.target).closest(".drp-calendar"),a=s.hasClass("left"),i=parseInt(s.find(".hourselect").val(),10),n=parseInt(s.find(".minuteselect").val(),10);isNaN(n)&&(n=parseInt(s.find(".minuteselect option:last").val(),10));var r=this.timePickerSeconds?parseInt(s.find(".secondselect").val(),10):0;if(!this.timePicker24Hour){var o=s.find(".ampmselect").val();"PM"===o&&i<12&&(i+=12),"AM"===o&&12===i&&(i=0)}if(a){var l=this.startDate.clone();l.hour(i),l.minute(n),l.second(r),this.setStartDate(l),this.singleDatePicker?this.endDate=this.startDate.clone():this.endDate&&this.endDate.format("YYYY-MM-DD")==l.format("YYYY-MM-DD")&&this.endDate.isBefore(l)&&this.setEndDate(l.clone())}else if(this.endDate){var h=this.endDate.clone();h.hour(i),h.minute(n),h.second(r),this.setEndDate(h)}this.updateCalendars(),this.updateFormInputs(),this.renderTimePicker("left"),this.renderTimePicker("right")},elementChanged:function(){if(this.element.is("input")&&this.element.val().length){var t=this.element.val().split(this.locale.separator),s=null,a=null;2===t.length&&(s=e(t[0],this.locale.format),a=e(t[1],this.locale.format)),(this.singleDatePicker||null===s||null===a)&&(a=s=e(this.element.val(),this.locale.format)),s.isValid()&&a.isValid()&&(this.setStartDate(s),this.setEndDate(a),this.updateView())}},keydown:function(e){9!==e.keyCode&&13!==e.keyCode||this.hide(),27===e.keyCode&&(e.preventDefault(),e.stopPropagation(),this.hide())},updateElement:function(){if(this.element.is("input")&&this.autoUpdateInput){var e=this.startDate.format(this.locale.format);this.singleDatePicker||(e+=this.locale.separator+this.endDate.format(this.locale.format)),e!==this.element.val()&&this.element.val(e).trigger("change")}},remove:function(){this.container.remove(),this.element.off(".daterangepicker"),this.element.removeData()}},t.fn.daterangepicker=function(e,a){var i=t.extend(!0,{},t.fn.daterangepicker.defaultOptions,e);return this.each((function(){var e=t(this);e.data("daterangepicker")&&e.data("daterangepicker").remove(),e.data("daterangepicker",new s(e,i,a))})),this},s}));const ha=$.fn.daterangepicker;$.fn.daterangepicker=function(e,t){return ha.call(this,e,t),e&&(e.showWeekNumbers||e.showISOWeekNumbers)&&this.each((function(){const e=$(this).data("daterangepicker");e&&e.container&&e.container.addClass("with-week-numbers")})),this}}},function(){return n||(0,i[e(i)[0]])((n={exports:{}}).exports,n),n.exports});export default r();
