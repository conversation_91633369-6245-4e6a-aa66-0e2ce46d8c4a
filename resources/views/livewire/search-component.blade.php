<div class="container mx-auto px-4 py-8">

    <section class="py-8 md:py-8 text-center bg-base-300">
        <div class="container mx-auto px-4 items-center">
            <div class="max-w-5xl mx-auto">
                <h1 class="text-3xl md:text-5xl font-bold mb-6 text-base-content leading-tight text-onsearch"
                    style="font-family: 'logo';">
                    بحث متقدم
                </h1>
            </div>
        </div>
    </section>
    <div class="container mx-auto px-4 py-8">

    <div class="flex flex-col lg:flex-row gap-8">
        
            <!-- Desktop Sidebar (Right Side) -->
            <div class="order-2 lg:order-1 w-full lg:w-1/4 space-y-4 hidden lg:block">

                <!-- Year Filter (Death Date) -->
                <div class="bg-base-200 rounded-lg p-4 border border-base-300">
                    <div>
                        <button class="flex justify-between items-center w-full font-semibold mb-2">
                            <span>وفاة المؤلف (هجري)</span>
                        </button>
                        <div class="mt-2">
                            <div class="flex gap-2">
                                <div class="w-1/2">
                                    <input id="year-from" type="number" class="input input-bordered w-full"
                                        placeholder="من" wire:model.live.debounce.500ms="yearFrom">
                                </div>
                                <div class="w-1/2">
                                    <input id="year-to" type="number" class="input input-bordered w-full"
                                        placeholder="إلى" wire:model.live.debounce.500ms="yearTo">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Categories Filter -->
                @if ($allCategories)
                    <div class="bg-base-200 rounded-lg p-4 mt-4 border border-base-300">
                        <div>
                            <button class="flex justify-between items-center w-full font-semibold mb-2">
                                <span>التصنيفات</span>
                            </button>
                            <div class="mt-2">
                                <div class="category-list ">
                                    @foreach ($allCategories as $category)
                                        <div class="form-control mb-1" wire:key="category-{{ $category->id }}">
                                            <label class="label cursor-pointer justify-start gap-2 py-1">
                                                <input type="checkbox" value="{{ $category->id }}"
                                                    class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                                    wire:model="categories">
                                                <span class="label-text">{{ $category->title }}
                                                    @if ($category->books_count != null)
                                                        ({{ $category->books_count }})
                                                    @endif
                                                </span>
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                @endif


                <!-- Books Filter -->
                <div class="bg-base-200 rounded-lg p-4 mt-4 border border-base-300">
                    <div>
                        <button class="flex justify-between items-center w-full font-semibold mb-2">
                            <span>الكتب</span>
                        </button>
                        <div class="mt-2">
                            <div class="relative mb-2">
                                <input type="text" class="input input-bordered w-full pr-10"
                                    placeholder="البحث عن كتاب" wire:model.live.debounce.500ms="bookSearchTerm">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="book-list min-h-[120px]">
                                @foreach ($allBooks->take($visibleBooksCount) as $book)
                                    <div class="form-control mb-1" wire:key="book-{{ $book->id }}">
                                        <label class="label cursor-pointer justify-start gap-2 py-1">
                                            <input type="checkbox" value="{{ $book->id }}"
                                                class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                                wire:model="books">
                                            <span class="label-text">{{ $book->title }}</span>
                                        </label>
                                    </div>
                                @endforeach
                                @if ($allBooks->count() > $visibleBooksCount)
                                    <button class="btn btn-ghost btn-sm w-full mt-2 text-primary text-lg"
                                        wire:click="loadMoreBooks">
                                        تحميل المزيد
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Authors Filter -->
                @if ($allAuthors)
                    <div class="bg-base-200 rounded-lg p-4 mt-4 border border-base-300">
                        <div>
                            <button class="flex justify-between items-center w-full font-semibold mb-2">
                                <span>المؤلفين</span>
                            </button>
                            <div class="mt-2">
                                <div class="relative mb-2">
                                    <input type="text" class="input input-bordered w-full pr-10"
                                        placeholder="البحث عن مؤلف" wire:model.live.debounce.500ms="authorSearchTerm">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="author-list min-h-[120px]">
                                    @foreach ($allAuthors->take($visibleAuthorsCount) as $author)
                                        <div class="form-control mb-1" wire:key="author-{{ $author->id }}">
                                            <label class="label cursor-pointer justify-start gap-2 py-1">
                                                <input type="checkbox" value="{{ $author->id }}"
                                                    class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                                    wire:model="authors">
                                                <span class="label-text">{{ $author->name }}
                                                    @if ($author->death_date && $author->death_date != 'معاصر')
                                                        <span class="text-xs text-base-content/70">(ت:
                                                            {{ $author->death_date }})</span>
                                                    @endif
                                                </span>
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                                @if ($allAuthors->count() > $visibleAuthorsCount)
                                    <button class="btn btn-ghost btn-sm w-full mt-2 text-primary text-lg"
                                        wire:click="loadMoreAuthors">
                                        تحميل المزيد
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif

            </div>


            <!-- Main Content (Search) -->
            <div class="lg:w-3/4 order-1 lg:order-2">
                <!-- Search Bar and sort-->
                <div class="hidden lg:flex flex-col md:flex-row gap-4 items-center">
                    <div class="relative flex-grow">
                        <div class="flex">
                            <input type="text" wire:model.live.debounce.500ms="query" placeholder="ابحث..."
                                class="h-10 input input-bordered w-full pr-10 focus:outline-primary bg-none border-base-300"
                                autocomplete="off" id="search-input">

                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <template x-if="$wire.isSearching">
                                    <div
                                        class="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full">
                                    </div>
                                </template>
                                <template x-if="!$wire.isSearching">
                                    <i class="fas fa-search text-gray-400"></i>
                                </template>
                            </div>
                        </div>


                    </div>
                    <div class="w-full md:w-auto">
                        <div class="dropdown">
                            @php
                                $sortLabels = [
                                    'newest' => 'الأحدث',
                                    'oldest' => 'الأقدم',
                                    'title_asc' => 'العنوان تصاعدي',
                                    'title_desc' => 'العنوان تنازلي',
                                    'author_asc' => 'المؤلف تصاعدي',
                                    'author_desc' => 'المؤلف تنازلي',
                                    'author_die_data_asc' => 'وفاة المؤلف تصاعدي',
                                    'author_die_data_desc' => 'وفاة المؤلف تنازلي',
                                ];
                            @endphp
                            <div tabindex="0"
                                class="h-10 btn btn-outline min-w-[200px] justify-between bg-none border-base-300 font-light">
                                {{ $sortLabels[$sort] ?? 'ترتيب حسب' }}
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-chevrons-up-down h-4 w-4 opacity-50" aria-hidden="true">
                                    <path d="m7 15 5 5 5-5"></path>
                                    <path d="m7 9 5-5 5 5"></path>
                                </svg>
                            </div>
                            <ul tabindex="0"
                                class="dropdown-content z-[1] menu shadow bg-base-100 rounded-box min-w-[200px] p-2 mt-1 border-base-300 border w-full">
                                <li><a value="newest" wire:click="$set('sort', 'newest')"
                                        class="sort-option {{ $sort == 'newest' ? 'active font-bold text-primary' : '' }}">الأحدث
                                        @if ($sort == 'newest')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="oldest" wire:click="$set('sort', 'oldest')"
                                        class="sort-option {{ $sort == 'oldest' ? 'active font-bold text-primary' : '' }}">الأقدم
                                        @if ($sort == 'oldest')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="title_asc" wire:click="$set('sort', 'title_asc')"
                                        class="sort-option {{ $sort == 'title_asc' ? 'active font-bold text-primary' : '' }}">العنوان
                                        تصاعدي @if ($sort == 'title_asc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="title_desc" wire:click="$set('sort', 'title_desc')"
                                        class="sort-option {{ $sort == 'title_desc' ? 'active font-bold text-primary' : '' }}">العنوان
                                        تنازلي @if ($sort == 'title_desc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="author_asc" wire:click="$set('sort', 'author_asc')"
                                        class="sort-option {{ $sort == 'author_asc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                        تصاعدي @if ($sort == 'author_asc')
                                        @endif
                                    </a>
                                </li>
                                <li><a value="author_desc" wire:click="$set('sort', 'author_desc')"
                                        class="sort-option {{ $sort == 'author_desc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                        تنازلي @if ($sort == 'author_desc')
                                        @endif
                                    </a></li>
                                <li><a value="author_die_data_asc" wire:click="$set('sort', 'author_die_data_asc')"
                                        class="sort-option {{ $sort == 'author_die_data_asc' ? 'active font-bold text-primary' : '' }}">وفاة
                                        المؤلف تصاعدي @if ($sort == 'author_die_data_asc')
                                        @endif
                                    </a></li>
                                <li><a value="author_die_data_desc" wire:click="$set('sort', 'author_die_data_desc')"
                                        class="sort-option {{ $sort == 'author_die_data_desc' ? 'active font-bold text-primary' : '' }}">وفاة
                                        المؤلف تنازلي @if ($sort == 'author_die_data_desc')
                                        @endif
                                    </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                              <!-- Mobile Filters Drawer -->
              <div id="mobile-filters-drawer" wire:ignore.self
                class="lg:hidden fixed top-0 right-0 w-80 h-full bg-base-100 shadow-xl z-50 transform translate-x-full transition-transform duration-300 ease-in-out overflow-y-auto">
                <div class="p-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">تصفية</h3>
                        <button class="btn btn-ghost btn-sm p-1"
                            onclick="document.getElementById('mobile-filters-drawer').classList.add('translate-x-full')">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Sort options removed from drawer -->

                    <!-- Death Date Filter -->
                    <div class="mb-4 border-t pt-4  hidden">
                        <h4 class="font-semibold mb-2">وفاة المؤلف (هجري)</h4>
                        <div class="mt-2">
                            <div class="relative w-full flex items-center mt-2 mb-4" style="height: 2.5rem;">
                                <input id="mobile-year-range-min" type="range" min="0" max="1446"
                                    value="{{ $yearFrom ?: 0 }}"
                                    class="range range-primary w-full absolute pointer-events-auto"
                                    style="z-index:2; background: transparent;"
                                    wire:change="$dispatch('year-range-changed', {type: 'min', value: $event.target.value})">
                                <input id="mobile-year-range-max" type="range" min="0" max="1446"
                                    value="{{ $yearTo ?: 1446 }}"
                                    class="range range-primary w-full absolute pointer-events-auto"
                                    style="z-index:1; background: transparent;"
                                    wire:change="$dispatch('year-range-changed', {type: 'max', value: $event.target.value})">
                            </div>
                            <div class="flex gap-2">
                                <div class="w-1/2">
                                    <input id="mobile-year-from" type="number" class="input input-bordered w-full"
                                        placeholder="من" wire:model="yearFrom">
                                </div>
                                <div class="w-1/2">
                                    <input id="mobile-year-to" type="number" class="input input-bordered w-full"
                                        placeholder="إلى" wire:model="yearTo">
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Categories Filter -->
                    @if ($allCategories)
                        <div class="mb-4  pt-4">
                            <h4 class="font-semibold mb-2">التصنيفات</h4>
                            <div class="category-list  overflow-y-auto">
                                @foreach ($allCategories as $category)
                                    <div class="form-control mb-1" wire:key="mobile-category-{{ $category->id }}">
                                        <label class="label cursor-pointer justify-start gap-2 py-1">
                                            <input type="checkbox" value="{{ $category->id }}"
                                                class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                                wire:model="categories">
                                            <span class="label-text">{{ $category->title }}
                                                @if ($category->books_count > 0)
                                                    <span class="badge badge-primary">{{ $category->books_count }}</span>
                                                @endif
                                            </span>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Authors Filter -->
                    @if ($allAuthors)
                        <div class="mb-4  pt-4 pb-16">
                            <h4 class="font-semibold mb-2">المؤلفين</h4>
                            <div class="relative mb-2">
                                <input type="text" class="input input-bordered w-full pr-10"
                                    placeholder="البحث عن مؤلف" wire:model.live.debounce.500ms="authorSearchTerm">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="author-list  overflow-y-auto">
                                @foreach ($allAuthors->take($visibleAuthorsCount) as $author)
                                    <div class="form-control mb-1" wire:key="mobile-author-{{ $author->id }}">
                                        <label class="label cursor-pointer justify-start gap-2 py-1">
                                            <input type="checkbox" value="{{ $author->id }}"
                                                class="checkbox checkbox-sm checkbox-primary rounded-xs"
                                                wire:model="authors">
                                            <span class="label-text">{{ $author->name }}
                                                @if ($author->death_date && $author->death_date != 'معاصر')
                                                    <span class="text-xs text-base-content/70">(ت:
                                                        {{ $author->death_date }})</span>
                                                @endif
                                            </span>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            @if ($allAuthors->count() > $visibleAuthorsCount)
                                <button class="btn btn-ghost btn-sm w-full mt-2 text-primary text-lg"
                                    wire:click="loadMoreAuthors">
                                    تحميل المزيد
                                </button>
                            @endif
                        </div>
                    @endif
                    <!-- Apply Filters Button -->
                    <div class="fixed bottom-0 left-0 right-0 pb-4 pt-2 bg-base-100 shadow-lg z-50 px-4 w-80">
                        <button class="btn btn-primary w-full" wire:click="$refresh"
                            onclick="document.getElementById('mobile-filters-drawer').classList.add('translate-x-full')">
                            تطبيق
                        </button>
                    </div>
                </div>
            </div>
                <div class="flex items-center gap-2 lg:hidden">
                    <div class="relative flex-grow">
                        <div class="flex">
                            <input type="text" wire:model.live.debounce.500ms="query" placeholder="ابحث..."
                                class="h-10 input input-bordered w-full pr-10 focus:outline-primary bg-none border-base-300"
                                autocomplete="off" id="search-input">

                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <template x-if="$wire.isSearching">
                                    <div
                                        class="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full">
                                    </div>
                                </template>
                                <template x-if="!$wire.isSearching">
                                    <i class="fas fa-search text-gray-400"></i>
                                </template>
                            </div>
                        </div>


                    </div>
                    <div class="dropdown dropdown-end">
                        <button class="btn btn-outline flex justify-between items-center">
                            <i class="fa-solid fa-arrow-down-wide-short"></i>
                        </button>
                        <ul tabindex="0"
                            class="dropdown-content z-[1] menu shadow bg-base-100 rounded-box min-w-[200px] p-2 mt-1 border-base-300 border w-full">
                            <li><a value="newest" wire:click="$set('sort', 'newest')"
                                    class="sort-option {{ $sort == 'newest' ? 'active font-bold text-primary' : '' }}">الأحدث
                                    @if ($sort == 'newest')
                                    @endif
                                </a>
                            </li>
                            <li><a value="oldest" wire:click="$set('sort', 'oldest')"
                                    class="sort-option {{ $sort == 'oldest' ? 'active font-bold text-primary' : '' }}">الأقدم
                                    @if ($sort == 'oldest')
                                    @endif
                                </a>
                            </li>
                            <li><a value="title_asc" wire:click="$set('sort', 'title_asc')"
                                    class="sort-option {{ $sort == 'title_asc' ? 'active font-bold text-primary' : '' }}">العنوان
                                    تصاعدي @if ($sort == 'title_asc')
                                    @endif
                                </a>
                            </li>
                            <li><a value="title_desc" wire:click="$set('sort', 'title_desc')"
                                    class="sort-option {{ $sort == 'title_desc' ? 'active font-bold text-primary' : '' }}">العنوان
                                    تنازلي @if ($sort == 'title_desc')
                                    @endif
                                </a>
                            </li>
                            <li><a value="author_asc" wire:click="$set('sort', 'author_asc')"
                                    class="sort-option {{ $sort == 'author_asc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                    تصاعدي @if ($sort == 'author_asc')
                                    @endif
                                </a>
                            </li>
                            <li><a value="author_desc" wire:click="$set('sort', 'author_desc')"
                                    class="sort-option {{ $sort == 'author_desc' ? 'active font-bold text-primary' : '' }}">المؤلف
                                    تنازلي @if ($sort == 'author_desc')
                                    @endif
                                </a></li>
                            <li><a value="author_die_data_asc" wire:click="$set('sort', 'author_die_data_asc')"
                                    class="sort-option {{ $sort == 'author_die_data_asc' ? 'active font-bold text-primary' : '' }}">وفاة
                                    المؤلف تصاعدي @if ($sort == 'author_die_data_asc')
                                    @endif
                                </a></li>
                            <li><a value="author_die_data_desc" wire:click="$set('sort', 'author_die_data_desc')"
                                    class="sort-option {{ $sort == 'author_die_data_desc' ? 'active font-bold text-primary' : '' }}">وفاة
                                    المؤلف تنازلي @if ($sort == 'author_die_data_desc')
                                    @endif
                                </a></li>
                        </ul>
                    </div>

                    <button onclick="document.getElementById('mobile-filters-drawer').classList.toggle('translate-x-full')"
                        class="btn btn-outline flex justify-between items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                        </svg>
                    </button>

                </div>

                <!-- Search Results Display -->
                <div class="bg-base-100 mt-4 rounded-lg  p-4 border border-base-300">

                    @if ($hasSearched && !empty($query))
                        <div class="mb-4 p-3 bg-background rounded-lg">
                            <p class="text-onsearch">
                                نتائج البحث عن: <span class="font-bold">{{ $query }}</span>
                                <span class="mx-2">|</span>
                                <span>عدد النتائج: {{ $resultsCount }}</span>
                                @if (!empty($categories) || !empty($authors) || !empty($yearFrom) || !empty($yearTo))
                                    <span class="mx-2">|</span>
                                    <span class="text-blue-600">مع فلاتر مطبقة</span>
                                @endif
                            </p>
                        </div>

                        <!-- Loading Indicator -->
                        <div x-show="$wire.isSearching" class="w-full py-4">
                            <div class="animate-pulse space-y-4">
                                <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                                <div class="flex flex-wrap gap-2">
                                    <div class="h-8 bg-gray-200 rounded-full w-24"></div>
                                    <div class="h-8 bg-gray-200 rounded-full w-32"></div>
                                    <div class="h-8 bg-gray-200 rounded-full w-20"></div>
                                </div>
                                <div class="h-4 bg-gray-200 rounded w-1/3 mt-4"></div>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div class="h-40 bg-gray-200 rounded-lg"></div>
                                    <div class="h-40 bg-gray-200 rounded-lg"></div>
                                    <div class="h-40 bg-gray-200 rounded-lg"></div>
                                    <div class="h-40 bg-gray-200 rounded-lg"></div>
                                    <div class="h-40 bg-gray-200 rounded-lg"></div>
                                    <div class="h-40 bg-gray-200 rounded-lg"></div>
                                </div>
                            </div>
                        </div>


                            @include('livewire.search-result-item', [
                                'query' => $query,
                                'expandedResultIndex' => $expandedResultIndex,
                                'expandedBook' => $expandedBook,
                                'expandedPage' => $expandedPage,
                                'expandedPart' => $expandedPart,
                                'expandedCurrentTitle' => $expandedCurrentTitle,
                                'expandedContentText' => $expandedContentText,
                                'expandedIsLoading' => $expandedIsLoading,
                                'expandedPagination' => $expandedPagination,
                            ])


                        @if (!empty($query) && strlen($query) > 2 && count($autoCompleteResults) === 0)
                            <div class=" mt-1  ">
                                <div class="p-4 text-center text-gray-600">
                                    <i class="fas fa-search-minus text-gray-400 text-2xl mb-2"></i>
                                    <p>لا توجد نتائج للبحث عن "{{ $query }}"</p>
                                    <p class="text-sm text-gray-500 mt-1">حاول استخدام كلمات مفتاحية أخرى</p>
                                </div>
                            </div>

                            <script>
                                document.addEventListener('livewire:initialized', function() {
                                    // Handle Livewire updates to close mobile filters drawer if open
                                    Livewire.hook('message.processed', (message, component) => {
                                        // Check if Alpine.js is available and the mobile filters drawer is open
                                        if (window.Alpine) {
                                            const searchComponent = document.querySelector('[x-data]');
                                            if (searchComponent && searchComponent.__x) {
                                                // Access the Alpine.js data
                                                const data = searchComponent.__x.getUnobservedData();
                                                // If a filter was changed and the drawer is open on mobile, close it
                                                if (data.mobileFiltersOpen && window.innerWidth < 1024) {
                                                    // Only close on certain updates (like filter changes)
                                                    if (message.updateQueue.some(update =>
                                                            update.payload.method === '$set' ||
                                                            update.payload.method === 'loadMoreBooks' ||
                                                            update.payload.method === 'loadMoreAuthors')) {
                                                        data.mobileFiltersOpen = false;
                                                    }
                                                }
                                            }
                                        }
                                    });
                                });
                            </script>
                        @endif

                        <!-- Custom Pagination -->
                        @php
                            $currentPage = $this->getCurrentPage();
                            $totalPages = $this->getTotalPages();
                            $pageNumbers = $this->getPageNumbers();
                        @endphp

                        @if ($resultsCount > 0 && $totalPages > 1)
                            <div class="mt-6 border-t border-base-300 pt-6">
                                <!-- Results Info -->
                                <div class="text-sm text-gray-600 text-center mb-4">
                                    عرض {{ $offset + 1 }} - {{ min($offset + $limit, $resultsCount) }} من
                                    {{ $resultsCount }} نتيجة
                                    (صفحة {{ $currentPage }} من {{ $totalPages }})
                                </div>

                                <!-- Pagination Controls -->
                                <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
                                    <!-- Mobile: Simple Previous/Next -->
                                    <div class="flex sm:hidden items-center gap-2 w-full justify-between">
                                        <button wire:click="previousPage"
                                            class="btn btn-sm btn-outline {{ $currentPage <= 1 ? 'btn-disabled' : '' }} flex-1"
                                            @if ($currentPage <= 1) disabled @endif>
                                            <i class="fas fa-chevron-right"></i>
                                            السابق
                                        </button>

                                        <span class="text-sm font-medium px-4">
                                            {{ $currentPage }} / {{ $totalPages }}
                                        </span>

                                        <button wire:click="nextPage"
                                            @if ($currentPage >= $totalPages) disabled @endif
                                            class="btn btn-sm btn-outline {{ $currentPage >= $totalPages ? 'btn-disabled' : '' }} flex-1">
                                            التالي
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                    </div>

                                    <!-- Desktop: Full Pagination -->
                                    <div class="hidden sm:flex items-center gap-2">
                                        <!-- Previous Button -->
                                        <button wire:click="previousPage"
                                            class="btn btn-sm btn-outline {{ $currentPage <= 1 ? 'btn-disabled' : '' }}"
                                            @if ($currentPage <= 1) disabled @endif>
                                            <i class="fas fa-chevron-right"></i>
                                            السابق
                                        </button>

                                        <!-- Page Numbers -->
                                        <div class="flex items-center gap-1">
                                            @foreach ($pageNumbers as $page)
                                                @if ($page === '...')
                                                    <span class="px-2 py-1 text-gray-500">...</span>
                                                @else
                                                    <button wire:click="goToPage({{ $page }})"
                                                        class="btn btn-sm {{ $currentPage == $page ? 'btn-primary' : 'btn-outline' }}">
                                                        {{ $page }}
                                                    </button>
                                                @endif
                                            @endforeach
                                        </div>

                                        <!-- Next Button -->
                                        <button wire:click="nextPage"
                                            @if ($currentPage >= $totalPages) disabled @endif
                                            class="btn btn-sm btn-outline {{ $currentPage >= $totalPages ? 'btn-disabled' : '' }}">
                                            التالي
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endif
                </div>
            </div>
        @else
            <!-- Recent Searches -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-3">عمليات البحث الأخيرة</h3>

                @if (count($recentSearches) > 0)
                    <div class="flex flex-wrap gap-2">
                        @foreach ($recentSearches as $search)
                            <button wire:click="selectRecentSearch('{{ $search['search_term'] }}')"
                                class="btn btn-sm btn-outline">
                                {{ $search['search_term'] }}
                            </button>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-sm">لا توجد عمليات بحث سابقة</p>
                @endif
            </div>

            <!-- Popular Searches -->
            <div>
                <h3 class="text-lg font-semibold mb-3">عمليات البحث الشائعة</h3>

                @if (count($popularSearches) > 0)
                    <div class="flex flex-wrap gap-2">
                        @foreach ($popularSearches as $search)
                            <button wire:click="selectRecentSearch('{{ $search['search_term'] }}')"
                                class="btn btn-sm btn-outline">
                                {{ $search['search_term'] }}
                            </button>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-sm">لا توجد عمليات بحث شائعة</p>
                @endif
            </div>
            @endif
        </div>
    </div>
</div>
</div>

<script>
    document.addEventListener('livewire:initialized', function() {
        // Preserve drawer state during Livewire updates

        // Sort functionality is now handled directly by Livewire wire:click
        // Handle year slider changes
        Livewire.on('year-range-changed', (data) => {
            aaa
            // For desktop sliders
            const yearFromInput = document.getElementById('year-from');
            const yearToInput = document.getElementById('year-to');

            // For mobile sliders
            const mobileYearFromInput = document.getElementById('mobile-year-from');
            const mobileYearToInput = document.getElementById('mobile-year-to');


            if (data.type === 'min') {

                Livewire.dispatch('input', {
                    id: yearFromInput?.getAttribute('wire:model')?.split('.')[0] ||
                        mobileYearFromInput?.getAttribute('wire:model')?.split('.')[0],
                    value: min
                });
            }

            if (data.type === 'max') {
                Livewire.dispatch('input', {
                    id: yearToInput?.getAttribute('wire:model')?.split('.')[0] ||
                        mobileYearToInput?.getAttribute('wire:model')?.split('.')[0],
                    value: max
                });
            }
        });

        // Add overlay for drawer
        const body = document.querySelector('body');
        let overlay;

        function createOverlay() {
            overlay = document.createElement('div');
            overlay.id = 'drawer-overlay';
            overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-40 hidden';
            overlay.addEventListener('click', closeDrawer);
            body.appendChild(overlay);
        }

        function closeDrawer() {
            const drawer = document.getElementById('mobile-filters-drawer');
            drawer.classList.add('translate-x-full');
            overlay.classList.add('hidden');
        }

        createOverlay();

        // Show/hide overlay when drawer is opened/closed
        const filterButton = document.querySelector('[onclick*="mobile-filters-drawer"]');
        const drawerCloseButtons = document.querySelectorAll('[onclick*="translate-x-full"]');

        if (filterButton) {
            filterButton.addEventListener('click', function() {
                overlay.classList.remove('hidden');
            });
        }

        drawerCloseButtons.forEach(button => {
            button.addEventListener('click', function() {
                overlay.classList.add('hidden');
            });
        });

        // Mobile sort functionality is now handled by the built-in dropdown component
    });
</script>
