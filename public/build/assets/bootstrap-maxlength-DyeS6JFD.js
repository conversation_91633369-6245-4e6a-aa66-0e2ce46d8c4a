var t;(t=jQuery).event.special.destroyed||(t.event.special.destroyed={remove:function(t){t.handler&&t.handler()}}),t.fn.extend({maxlength:function(e,a){var s=t("body");function n(t){var e=t.charCodeAt();return e?e<128?1:e<2048?2:3:0}function o(t){return t.split("").map(n).concat(0).reduce((function(t,e){return t+e}))}function r(t){var a=t.val();a=e.twoCharLinebreak?a.replace(/\r(?!\n)|\n(?!\r)/g,"\r\n"):a.replace(/(?:\r\n|\r|\n)/g,"\n");var s=0;return s=e.utf8?o(a):a.length,"file"===t.prop("type")&&""!==t.val()&&(s-=12),s}function i(t,e){return e-r(t)}function l(t,e){e.css({display:"block"}),t.trigger("maxlength.shown")}function c(t,a,s){var n="";return e.message?n="function"==typeof e.message?e.message(t,a):e.message.replace("%charsTyped%",s).replace("%charsRemaining%",a-s).replace("%charsTotal%",a):(e.preText&&(n+=e.preText),e.showCharsTyped?n+=s:n+=a-s,e.showMaxLength&&(n+=e.separator+a),e.postText&&(n+=e.postText)),n}function p(t,a,s,n){var o,i,p,h;n&&(n.html(c(a.val(),s,s-t)),t>0?(o=a,i=e.threshold,p=s,h=!0,!e.alwaysShow&&p-r(o)>i&&(h=!1),h?l(a,n.removeClass(e.limitReachedClass+" "+e.limitExceededClass).addClass(e.warningClass)):function(t,a){e.alwaysShow||(a.css({display:"none"}),t.trigger("maxlength.hidden"))}(a,n)):e.limitExceededClass?l(a,0===t?n.removeClass(e.warningClass+" "+e.limitExceededClass).addClass(e.limitReachedClass):n.removeClass(e.warningClass+" "+e.limitReachedClass).addClass(e.limitExceededClass)):l(a,n.removeClass(e.warningClass).addClass(e.limitReachedClass))),e.customMaxAttribute&&(t<0?a.addClass(e.customMaxClass):a.removeClass(e.customMaxClass))}function h(a,s){var n=function(e){var a=e[0];return t.extend({},"function"==typeof a.getBoundingClientRect?a.getBoundingClientRect():{width:a.offsetWidth,height:a.offsetHeight},e.offset())}(a);if("function"!==t.type(e.placement))if(t.isPlainObject(e.placement))!function(a,s){if(a&&s){var n={};t.each(["top","bottom","left","right","position"],(function(t,a){var s=e.placement[a];void 0!==s&&(n[a]=s)})),s.css(n)}}(e.placement,s);else{var o=a.outerWidth(),r=s.outerWidth(),i=s.width(),l=s.height();switch(e.appendToParent&&(n.top-=a.parent().offset().top,n.left-=a.parent().offset().left),e.placement){case"bottom":s.css({top:n.top+n.height,left:n.left+n.width/2-i/2});break;case"top":s.css({top:n.top-l,left:n.left+n.width/2-i/2});break;case"left":s.css({top:n.top+n.height/2-l/2,left:n.left-i});break;case"right":s.css({top:n.top+n.height/2-l/2,left:n.left+n.width});break;case"bottom-right":s.css({top:n.top+n.height,left:n.left+n.width});break;case"top-right":s.css({top:n.top-l,left:n.left+o});break;case"top-left":s.css({top:n.top-l,left:n.left-r});break;case"bottom-left":s.css({top:n.top+a.outerHeight(),left:n.left-r});break;case"centered-right":s.css({top:n.top+l/2,left:n.left+o-r-3});break;case"bottom-right-inside":s.css({top:n.top+n.height,left:n.left+n.width-r});break;case"top-right-inside":s.css({top:n.top-l,left:n.left+o-r});break;case"top-left-inside":s.css({top:n.top-l,left:n.left});break;case"bottom-left-inside":s.css({top:n.top+a.outerHeight(),left:n.left})}}else e.placement(a,s,n)}function d(t){var a=t.attr("maxlength")||e.customMaxAttribute;if(e.customMaxAttribute&&!e.allowOverMax){var s=t.attr(e.customMaxAttribute);(!a||s<a)&&(a=s)}return a||(a=t.attr("size")),a}return t.isFunction(e)&&!a&&(a=e,e={}),e=t.extend({showOnReady:!1,alwaysShow:!0,threshold:0,warningClass:"small form-text text-muted",limitReachedClass:"small form-text text-danger",limitExceededClass:"",separator:" / ",preText:"",postText:"",showMaxLength:!0,placement:"bottom-right-inside",message:null,showCharsTyped:!0,validate:!1,utf8:!1,appendToParent:!1,twoCharLinebreak:!0,customMaxAttribute:null,customMaxClass:"overmax",allowOverMax:!1,zIndex:1099},e),this.each((function(){var a,r,l=t(this);function f(){var n=c(l.val(),a,"0");a=d(l),r||(r=t('<span class="bootstrap-maxlength"></span>').css({display:"none",position:"absolute",whiteSpace:"nowrap",zIndex:e.zIndex}).html(n)),l.is("textarea")&&(l.data("maxlenghtsizex",l.outerWidth()),l.data("maxlenghtsizey",l.outerHeight()),l.mouseup((function(){l.outerWidth()===l.data("maxlenghtsizex")&&l.outerHeight()===l.data("maxlenghtsizey")||h(l,r),l.data("maxlenghtsizex",l.outerWidth()),l.data("maxlenghtsizey",l.outerHeight())}))),e.appendToParent?(l.parent().append(r),l.parent().css("position","relative")):s.append(r),p(i(l,d(l)),l,a,r),h(l,r)}t(window).resize((function(){r&&h(l,r)})),e.showOnReady?l.ready((function(){f()})):l.focus((function(){f()})),l.on("maxlength.reposition",(function(){h(l,r)})),l.on("destroyed",(function(){r&&r.remove()})),l.on("blur",(function(){r&&!e.showOnReady&&r.remove()})),l.on("input",(function(){var t=d(l),s=i(l,t),c=!0;return e.validate&&s<0?(function(t,a){var s=t.val();if(e.twoCharLinebreak&&"\n"===(s=s.replace(/\r(?!\n)|\n(?!\r)/g,"\r\n"))[s.length-1]&&(a-=s.length%2),e.utf8){for(var r=s.split("").map(n),i=0,l=o(s)-a;i<l;i+=r.pop());a-=a-r.length}t.val(s.substr(0,a))}(l,t),c=!1):p(s,l,a,r),c}))}))}});
