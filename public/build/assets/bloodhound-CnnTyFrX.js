import{c as t,g as e}from"./_commonjsHelpers-MdiGH4nz.js";import{r}from"./jquery-DFf3aua2.js";var i,n,s,o,u,a,c,h,l,f,p,d={exports:{}};
/*!
 * typeahead.js 0.11.1
 * https://github.com/twitter/typeahead.js
 * Copyright 2013-2015 Twitter, Inc. and other contributors; Licensed MIT
 */const m=e(d.exports=(i=r(),n={isMsie:function(){return!!/(msie|trident)/i.test(navigator.userAgent)&&navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]},isBlankString:function(t){return!t||/^\s*$/.test(t)},escapeRegExChars:function(t){return t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isArray:i.isArray,isFunction:i.isFunction,isObject:i.isPlainObject,isUndefined:function(t){return void 0===t},isElement:function(t){return!(!t||1!==t.nodeType)},isJQuery:function(t){return t instanceof i},toStr:function(t){return n.isUndefined(t)||null===t?"":t+""},bind:i.proxy,each:function(t,e){function r(t,r){return e(r,t)}i.each(t,r)},map:i.map,filter:i.grep,every:function(t,e){var r=!0;return t?(i.each(t,(function(i,n){if(!(r=e.call(null,n,i,t)))return!1})),!!r):r},some:function(t,e){var r=!1;return t?(i.each(t,(function(i,n){if(r=e.call(null,n,i,t))return!1})),!!r):r},mixin:i.extend,identity:function(t){return t},clone:function(t){return i.extend(!0,{},t)},getIdGenerator:function(){var t=0;return function(){return t++}},templatify:function(t){return i.isFunction(t)?t:e;function e(){return String(t)}},defer:function(t){setTimeout(t,0)},debounce:function(t,e,r){var i,n;return function(){var s,o,u=this,a=arguments;return s=function(){i=null,r||(n=t.apply(u,a))},o=r&&!i,clearTimeout(i),i=setTimeout(s,e),o&&(n=t.apply(u,a)),n}},throttle:function(t,e){var r,i,n,s,o,u;return o=0,u=function(){o=new Date,n=null,s=t.apply(r,i)},function(){var a=new Date,c=e-(a-o);return r=this,i=arguments,c<=0?(clearTimeout(n),n=null,o=a,s=t.apply(r,i)):n||(n=setTimeout(u,c)),s}},stringify:function(t){return n.isString(t)?t:JSON.stringify(t)},noop:function(){}},s="0.11.1",o=function(){return{nonword:e,whitespace:t,obj:{nonword:r(e),whitespace:r(t)}};function t(t){return(t=n.toStr(t))?t.split(/\s+/):[]}function e(t){return(t=n.toStr(t))?t.split(/\W+/):[]}function r(t){return function(e){return e=n.isArray(e)?e:[].slice.call(arguments,0),function(r){var i=[];return n.each(e,(function(e){i=i.concat(t(n.toStr(r[e])))})),i}}}}(),u=function(){function t(t){this.maxSize=n.isNumber(t)?t:100,this.reset(),this.maxSize<=0&&(this.set=this.get=i.noop)}function e(){this.head=this.tail=null}function r(t,e){this.key=t,this.val=e,this.prev=this.next=null}return n.mixin(t.prototype,{set:function(t,e){var i,n=this.list.tail;this.size>=this.maxSize&&(this.list.remove(n),delete this.hash[n.key],this.size--),(i=this.hash[t])?(i.val=e,this.list.moveToFront(i)):(i=new r(t,e),this.list.add(i),this.hash[t]=i,this.size++)},get:function(t){var e=this.hash[t];if(e)return this.list.moveToFront(e),e.val},reset:function(){this.size=0,this.hash={},this.list=new e}}),n.mixin(e.prototype,{add:function(t){this.head&&(t.next=this.head,this.head.prev=t),this.head=t,this.tail=this.tail||t},remove:function(t){t.prev?t.prev.next=t.next:this.head=t.next,t.next?t.next.prev=t.prev:this.tail=t.prev},moveToFront:function(t){this.remove(t),this.add(t)}}),t}(),a=function(){var t;try{(t=window.localStorage).setItem("~~~","!"),t.removeItem("~~~")}catch(a){t=null}function e(e,r){this.prefix=["__",e,"__"].join(""),this.ttlKey="__ttl__",this.keyMatcher=new RegExp("^"+n.escapeRegExChars(this.prefix)),this.ls=r||t,!this.ls&&this._noop()}return n.mixin(e.prototype,{_prefix:function(t){return this.prefix+t},_ttlKey:function(t){return this._prefix(t)+this.ttlKey},_noop:function(){this.get=this.set=this.remove=this.clear=this.isExpired=n.noop},_safeSet:function(t,e){try{this.ls.setItem(t,e)}catch(a){"QuotaExceededError"===a.name&&(this.clear(),this._noop())}},get:function(t){return this.isExpired(t)&&this.remove(t),o(this.ls.getItem(this._prefix(t)))},set:function(t,e,i){return n.isNumber(i)?this._safeSet(this._ttlKey(t),s(r()+i)):this.ls.removeItem(this._ttlKey(t)),this._safeSet(this._prefix(t),s(e))},remove:function(t){return this.ls.removeItem(this._ttlKey(t)),this.ls.removeItem(this._prefix(t)),this},clear:function(){var t,e=u(this.keyMatcher);for(t=e.length;t--;)this.remove(e[t]);return this},isExpired:function(t){var e=o(this.ls.getItem(this._ttlKey(t)));return!!(n.isNumber(e)&&r()>e)}}),e;function r(){return(new Date).getTime()}function s(t){return JSON.stringify(n.isUndefined(t)?null:t)}function o(t){return i.parseJSON(t)}function u(e){var r,i,n=[],s=t.length;for(r=0;r<s;r++)(i=t.key(r)).match(e)&&n.push(i.replace(e,""));return n}}(),c=function(){var t=0,e={},r=6,s=new u(10);function o(t){t=t||{},this.cancelled=!1,this.lastReq=null,this._send=t.transport,this._get=t.limiter?t.limiter(this._get):this._get,this._cache=!1===t.cache?new u(0):s}return o.setMaxPendingRequests=function(t){r=t},o.resetCache=function(){s.reset()},n.mixin(o.prototype,{_fingerprint:function(t){return(t=t||{}).url+t.type+i.param(t.data||{})},_get:function(i,n){var s,o,u=this;function a(t){n(null,t),u._cache.set(s,t)}function c(){n(!0)}function h(){t--,delete e[s],u.onDeckRequestArgs&&(u._get.apply(u,u.onDeckRequestArgs),u.onDeckRequestArgs=null)}s=this._fingerprint(i),this.cancelled||s!==this.lastReq||((o=e[s])?o.done(a).fail(c):t<r?(t++,e[s]=this._send(i).done(a).fail(c).always(h)):this.onDeckRequestArgs=[].slice.call(arguments,0))},get:function(t,e){var r,s;e=e||i.noop,t=n.isString(t)?{url:t}:t||{},s=this._fingerprint(t),this.cancelled=!1,this.lastReq=s,(r=this._cache.get(s))?e(null,r):this._get(t,e)},cancel:function(){this.cancelled=!0}}),o}(),h=window.SearchIndex=function(){var t="c",e="i";function r(t){(t=t||{}).datumTokenizer&&t.queryTokenizer||i.error("datumTokenizer and queryTokenizer are both required"),this.identify=t.identify||n.stringify,this.datumTokenizer=t.datumTokenizer,this.queryTokenizer=t.queryTokenizer,this.reset()}return n.mixin(r.prototype,{bootstrap:function(t){this.datums=t.datums,this.trie=t.trie},add:function(r){var i=this;r=n.isArray(r)?r:[r],n.each(r,(function(r){var u,a;i.datums[u=i.identify(r)]=r,a=s(i.datumTokenizer(r)),n.each(a,(function(r){var n,s,a;for(n=i.trie,s=r.split("");a=s.shift();)(n=n[t][a]||(n[t][a]=o()))[e].push(u)}))}))},get:function(t){var e=this;return n.map(t,(function(t){return e.datums[t]}))},search:function(r){var i,o,c=this;return i=s(this.queryTokenizer(r)),n.each(i,(function(r){var i,n,s,u;if(o&&0===o.length)return!1;for(i=c.trie,n=r.split("");i&&(s=n.shift());)i=i[t][s];if(!i||0!==n.length)return o=[],!1;u=i[e].slice(0),o=o?a(o,u):u})),o?n.map(u(o),(function(t){return c.datums[t]})):[]},all:function(){var t=[];for(var e in this.datums)t.push(this.datums[e]);return t},reset:function(){this.datums={},this.trie=o()},serialize:function(){return{datums:this.datums,trie:this.trie}}}),r;function s(t){return t=n.filter(t,(function(t){return!!t})),t=n.map(t,(function(t){return t.toLowerCase()}))}function o(){var r={};return r[e]=[],r[t]={},r}function u(t){for(var e={},r=[],i=0,n=t.length;i<n;i++)e[t[i]]||(e[t[i]]=!0,r.push(t[i]));return r}function a(t,e){var r=0,i=0,n=[];t=t.sort(),e=e.sort();for(var s=t.length,o=e.length;r<s&&i<o;)t[r]<e[i]?r++:(t[r]>e[i]||(n.push(t[r]),r++),i++);return n}}(),l=function(){var t;function e(t){this.url=t.url,this.ttl=t.ttl,this.cache=t.cache,this.prepare=t.prepare,this.transform=t.transform,this.transport=t.transport,this.thumbprint=t.thumbprint,this.storage=new a(t.cacheKey)}return t={data:"data",protocol:"protocol",thumbprint:"thumbprint"},n.mixin(e.prototype,{_settings:function(){return{url:this.url,type:"GET",dataType:"json"}},store:function(e){this.cache&&(this.storage.set(t.data,e,this.ttl),this.storage.set(t.protocol,location.protocol,this.ttl),this.storage.set(t.thumbprint,this.thumbprint,this.ttl))},fromCache:function(){var e,r={};return this.cache?(r.data=this.storage.get(t.data),r.protocol=this.storage.get(t.protocol),r.thumbprint=this.storage.get(t.thumbprint),e=r.thumbprint!==this.thumbprint||r.protocol!==location.protocol,r.data&&!e?r.data:null):null},fromNetwork:function(t){var e,r=this;function i(){t(!0)}function n(e){t(null,r.transform(e))}t&&(e=this.prepare(this._settings()),this.transport(e).fail(i).done(n))},clear:function(){return this.storage.clear(),this}}),e}(),f=function(){function t(t){this.url=t.url,this.prepare=t.prepare,this.transform=t.transform,this.transport=new c({cache:t.cache,limiter:t.limiter,transport:t.transport})}return n.mixin(t.prototype,{_settings:function(){return{url:this.url,type:"GET",dataType:"json"}},get:function(t,e){var r,i=this;if(e)return t=t||"",r=this.prepare(t,this._settings()),this.transport.get(r,n);function n(t,r){e(t?[]:i.transform(r))}},cancelLastRequest:function(){this.transport.cancel()}}),t}(),p=function(){return function(r){var s,o;return s={initialize:!0,identify:n.stringify,datumTokenizer:null,queryTokenizer:null,sufficient:5,sorter:null,local:[],prefetch:null,remote:null},!(r=n.mixin(s,r||{})).datumTokenizer&&i.error("datumTokenizer is required"),!r.queryTokenizer&&i.error("queryTokenizer is required"),o=r.sorter,r.sorter=o?function(t){return t.sort(o)}:n.identity,r.local=n.isFunction(r.local)?r.local():r.local,r.prefetch=t(r.prefetch),r.remote=e(r.remote),r};function t(t){var e;return t?(e={url:null,ttl:864e5,cache:!0,cacheKey:null,thumbprint:"",prepare:n.identity,transform:n.identity,transport:null},t=n.isString(t)?{url:t}:t,!(t=n.mixin(e,t)).url&&i.error("prefetch requires url to be set"),t.transform=t.filter||t.transform,t.cacheKey=t.cacheKey||t.url,t.thumbprint=s+t.thumbprint,t.transport=t.transport?u(t.transport):i.ajax,t):null}function e(t){var e;if(t)return e={url:null,cache:!0,prepare:null,replace:null,wildcard:null,limiter:null,rateLimitBy:"debounce",rateLimitWait:300,transform:n.identity,transport:null},t=n.isString(t)?{url:t}:t,!(t=n.mixin(e,t)).url&&i.error("remote requires url to be set"),t.transform=t.filter||t.transform,t.prepare=r(t),t.limiter=o(t),t.transport=t.transport?u(t.transport):i.ajax,delete t.replace,delete t.wildcard,delete t.rateLimitBy,delete t.rateLimitWait,t}function r(t){var e,r,i;return e=t.prepare,r=t.replace,i=t.wildcard,e||(e=r?n:t.wildcard?s:o);function n(t,e){return e.url=r(e.url,t),e}function s(t,e){return e.url=e.url.replace(i,encodeURIComponent(t)),e}function o(t,e){return e}}function o(t){var e,r,i;return e=t.limiter,r=t.rateLimitBy,i=t.rateLimitWait,e||(e=/^throttle$/i.test(r)?o(i):s(i)),e;function s(t){return function(e){return n.debounce(e,t)}}function o(t){return function(e){return n.throttle(e,t)}}}function u(t){return function(e){var r=i.Deferred();return t(e,s,o),r;function s(t){n.defer((function(){r.resolve(t)}))}function o(t){n.defer((function(){r.reject(t)}))}}}}(),function(){var t;function e(t){t=p(t),this.sorter=t.sorter,this.identify=t.identify,this.sufficient=t.sufficient,this.local=t.local,this.remote=t.remote?new f(t.remote):null,this.prefetch=t.prefetch?new l(t.prefetch):null,this.index=new h({identify:this.identify,datumTokenizer:t.datumTokenizer,queryTokenizer:t.queryTokenizer}),!1!==t.initialize&&this.initialize()}return t=window&&window.Bloodhound,e.noConflict=function(){return window&&(window.Bloodhound=t),e},e.tokenizers=o,n.mixin(e.prototype,{__ttAdapter:function(){var t=this;return this.remote?e:r;function e(e,r,i){return t.search(e,r,i)}function r(e,r){return t.search(e,r)}},_loadPrefetch:function(){var t,e,r=this;return t=i.Deferred(),this.prefetch?(e=this.prefetch.fromCache())?(this.index.bootstrap(e),t.resolve()):this.prefetch.fromNetwork(n):t.resolve(),t.promise();function n(e,i){if(e)return t.reject();r.add(i),r.prefetch.store(r.index.serialize()),t.resolve()}},_initialize:function(){var t=this;return this.clear(),(this.initPromise=this._loadPrefetch()).done(e),this.initPromise;function e(){t.add(t.local)}},initialize:function(t){return!this.initPromise||t?this._initialize():this.initPromise},add:function(t){return this.index.add(t),this},get:function(t){return t=n.isArray(t)?t:[].slice.call(arguments),this.index.get(t)},search:function(t,e,r){var i,s=this;return i=this.sorter(this.index.search(t)),e(this.remote?i.slice():i),this.remote&&i.length<this.sufficient?this.remote.get(t,o):this.remote&&this.remote.cancelLastRequest(),this;function o(t){var e=[];n.each(t,(function(t){!n.some(i,(function(e){return s.identify(t)===s.identify(e)}))&&e.push(t)})),r&&r(e)}},all:function(){return this.index.all()},clear:function(){return this.index.reset(),this},clearPrefetchCache:function(){return this.prefetch&&this.prefetch.clear(),this},clearRemoteCache:function(){return c.resetCache(),this},ttAdapter:function(){return this.__ttAdapter()}}),e}()));try{window.Bloodhound=m}catch(y){}
