<?php

namespace App\Models;

use App\Http\Controllers\BookController;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BookSearch extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $table = 'book_searches';
    protected $connection = 'sqlite_search';

    public function book()
    {
        return $this->belongsTo(Book::class);
    }

    public static function search($term, $searchIn, $currentBookId = null, $booksToExcloud = [], $booksToInclude = [], $limit = 12, $offset = 0)
    {
        $allowedBooks = BookController::allowedBooksIdsToSearch();
        $disAllowedBooks = BookController::disallowedBookIdsToSearch();

        $connection = 'sqlite_search';
        $db = DB::connection($connection);

        //handle SQL injection remove unwanted characters
        $term = preg_replace('/[^A-Za-z0-9\p{Arabic}]/u', ' ', $term);
        $term = preg_replace('/\s+/', ' ', $term);
        $term = trim($term);

        if ($term == '') {
            return [];
        }

        //check if empty search
        if (empty($term)) {
            return [];
        }

        $searchFtsModel = new SearchFtsModel($term, $searchIn, $limit, 'exact', false, true, $offset);

        // Add book ID filter for current book search
        if ($searchIn === 'current_book' && $currentBookId) {
            $searchFtsModel->bookIdsToInclude = [$currentBookId];
        }

        if (!empty($booksToExcloud)) {
            $searchFtsModel->bookIdsToExclude = $booksToExcloud;
        }
        if (!empty($booksToInclude)) {
            $searchFtsModel->bookIdsToInclude = $booksToInclude;
        }

        if (empty($booksToExcloud) && empty($booksToInclude)) {
            // check whats bigger to ignored
            if (count($allowedBooks) > count($disAllowedBooks)) {
                $searchFtsModel->bookIdsToInclude = $allowedBooks;
            } else {
                $searchFtsModel->bookIdsToExclude = $disAllowedBooks;
            }
        }

        $booksIdsToExcloudAsNoPublic = Book::where('application', 0)->pluck('id')->toArray();
        if (!empty($booksIdsToExcloudAsNoPublic)) {
            if (!empty($searchFtsModel->bookIdsToExclude)) {
                $searchFtsModel->bookIdsToExclude = array_merge($searchFtsModel->bookIdsToExclude, $booksIdsToExcloudAsNoPublic);
            } else {
                $searchFtsModel->bookIdsToExclude = $booksIdsToExcloudAsNoPublic;
            }
        }

        $query = self::generateQuery($searchFtsModel, '', '*');

        Log::info('Search Query: ' . $query);

        $rows = $db->select($query);

        $ids = [];
        foreach ($rows as $row) {
            $ids[] = $row->id;
        }

        if (empty($ids)) {
            return [];
        }

        $queryDetails = $db->select('SELECT * FROM book_searches WHERE id IN (' . implode(',', $ids) . ')');

        $results = [];
        foreach ($rows as $row) {
            $result = $row;
            foreach ($queryDetails as $queryDetail) {
                if ($queryDetail->id == $row->id) {
                    $result->book_title = $queryDetail->book_title;
                    $result->book_id = $queryDetail->book_id;
                    $result->page = $queryDetail->page;
                    $result->page_id = $queryDetail->page_id;
                    $result->page_title = $queryDetail->page_title;
                    $result->part = $queryDetail->part;
                    $result->href = url('book/' . $result->book_id . '/page/' . $result->page . '/' . $result->part);
                }
            }
            $results[] = $result;
        }
        return $results;
    }

    public static function generateQuery($searchFtsModel, $whereColumn, $selectFields = '*')
    {
        $query = '';
        $sortByQuery = self::generateSortByQuery($searchFtsModel);

        if ($searchFtsModel->searchInBooksType == 'exact') {
            $whereQ = self::generateWhereQuery($searchFtsModel, $whereColumn);
            $query = "
            SELECT $selectFields, pirority FROM (
                SELECT $selectFields, snippet(books_search_fts,2, '<b style=\"color:red\">', '</b>', '...', 100) AS snip, 1 AS pirority
                FROM books_search_fts $whereQ
            )
            $sortByQuery
            LIMIT {$searchFtsModel->limit}
            OFFSET {$searchFtsModel->offset}
        ";
        } elseif ($searchFtsModel->searchInBooksType == 'root') {
            $whereQ = self::generateWhereQueryRoot($searchFtsModel, $whereColumn);
            if ($selectFields != '*') {
                $query = "
                SELECT $selectFields FROM books_search_fts
                $whereQ
                $sortByQuery
                LIMIT {$searchFtsModel->limit}
                OFFSET {$searchFtsModel->offset}
            ";
            } else {
                $query = "
                SELECT offsets(books_search_fts),* FROM books_search_fts
                $whereQ
                $sortByQuery
                LIMIT {$searchFtsModel->limit}
                OFFSET {$searchFtsModel->offset}
            ";
            }
        } else {
            $whereQueries = self::generateWhereQueriesForDefaultSearch($searchFtsModel, $whereColumn);
            $query = "SELECT $selectFields, pirority FROM (";
            foreach ($whereQueries as $i => $whereQ) {
                $query .= "SELECT $selectFields, snippet(books_search_fts,2, '<b style=\"color:red\">', '</b>', '...', 100) AS snip, $i AS pirority FROM books_search_fts $whereQ " . ($i == 2 ? "LIMIT {$searchFtsModel->limit} OFFSET {$searchFtsModel->offset}" : '') . ' UNION ALL ';
            }
            $query = substr($query, 0, -10);
            $query .= ') AS combined_results ' . ($sortByQuery ? $sortByQuery : 'ORDER BY pirority');
        }

        return $query;
    }

    public static function generateSortByQuery($searchFtsModel)
    {
        $sortByQuery = '';
        if ($searchFtsModel->sortBy == 'اسم الكتاب') {
            $sortByQuery = 'ORDER BY book_title ' . ($searchFtsModel->sortAsc ? 'ASC' : 'DESC');
        }
        if ($searchFtsModel->sortBy == 'سنة الوفاة') {
            $sortByQuery = 'ORDER BY author_die_date ' . ($searchFtsModel->sortAsc ? 'ASC' : 'DESC');
        }
        if ($searchFtsModel->sortBy == 'افتراضي') {
            $sortByQuery = '';
        }
        return $sortByQuery;
    }
    public static function generateWhereQuery($searchFtsModel, $whereColumn)
    {
        $search = trim($searchFtsModel->search);
        $whereQ = '"' . trim($search) . '"';

        if ($searchFtsModel->searchInBooksType == 'exact') {
            if (!empty($searchFtsModel->conditions) && count($searchFtsModel->conditions) == count($searchFtsModel->conditionsOperators)) {
                for ($i = 0; $i < count($searchFtsModel->conditions); $i++) {
                    $conditionText = $searchFtsModel->conditions[$i];
                    if (trim($conditionText) == '') {
                        continue;
                    }
                    $conditionOperator = $searchFtsModel->conditionsOperators[$i];
                    if ($conditionOperator == 'و') {
                        $whereQ .= ' "' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'أو') {
                        $whereQ .= ' OR "' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'ليس') {
                        $whereQ .= '-"' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'قريب') {
                        $whereQ .= ' NEAR/5 "' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'ليس') {
                        $whereQ = str_replace('"', '', $whereQ);
                    }
                }
            }
            if (empty($whereColumn)) {
                if ($searchFtsModel->enableSearchInFooter) {
                    $whereQ = "WHERE ((search MATCH ('$whereQ')) OR (search_footer MATCH ('$whereQ')))";
                } else {
                    $whereQ = "WHERE search MATCH '$whereQ'";
                }
            } else {
                $whereQ = "WHERE $whereColumn MATCH '$whereQ'";
            }
        }

        if ($searchFtsModel->searchInBooksType == 'root') {
            $whereQ = '"' . trim(implode('" "', $searchFtsModel->searchWordsAsRoot)) . '"';
            if (!empty($searchFtsModel->conditions) && count($searchFtsModel->conditions) == count($searchFtsModel->conditionsOperators)) {
                for ($i = 0; $i < count($searchFtsModel->conditions); $i++) {
                    $conditionText = $searchFtsModel->conditions[$i];
                    if (trim($conditionText) == '') {
                        continue;
                    }
                    $conditionOperator = $searchFtsModel->conditionsOperators[$i];
                    if ($conditionOperator == 'و') {
                        $whereQ .= ' "' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'أو') {
                        $whereQ .= ' OR "' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'ليس') {
                        $whereQ .= '-"' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'قريب') {
                        $whereQ .= ' NEAR/5 "' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'ليس') {
                        $whereQ = str_replace('"', '', $whereQ);
                    }
                }
            }
            if (empty($whereColumn)) {
                if ($searchFtsModel->enableSearchInFooter) {
                    $whereQ = "WHERE (search MATCH '$whereQ' OR search_footer MATCH '$whereQ')";
                } else {
                    $whereQ = "WHERE search MATCH ('$whereQ')";
                }
            } else {
                $whereQ = "WHERE $whereColumn MATCH '$whereQ'";
            }
        }

        if (count($searchFtsModel->idsToExclude) > 0) {
            $whereQ .= ' AND (id NOT IN (' . implode(',', $searchFtsModel->idsToExclude) . '))';
        }

        if (count($searchFtsModel->bookIdsToExclude) > 0) {
            $whereQ .= ' AND (book_id NOT IN (' . implode(',', $searchFtsModel->bookIdsToExclude) . '))';
        }

        if (count($searchFtsModel->bookIdsToInclude) > 0) {
            $whereQ .= ' AND (book_id IN (' . implode(',', $searchFtsModel->bookIdsToInclude) . '))';
        }

        $whereQ = str_replace('xyz', '', $whereQ);
        return $whereQ;
    }

    public static function generateWhereQueryRoot($searchFtsModel, $whereColumn)
    {
        // $searchAsWordsRoot = array_unique($searchFtsModel->search);
        // $moaagmDatabaseController = App::make('MoaagmDatabaseController');

        $whereQ = '"' . trim(implode(' ', $searchFtsModel->search)) . '"';
        if (!empty($searchFtsModel->conditions) && count($searchFtsModel->conditions) == count($searchFtsModel->conditionsOperators)) {
            for ($i = 0; $i < count($searchFtsModel->conditions); $i++) {
                $conditionText = $searchFtsModel->conditions[$i];
                $conditionAsWordsRoot = [];
                $conditionWords = explode(' ', $conditionText);
                for ($j = 0; $j < count($conditionWords); $j++) {
                    if ($j > 3) {
                        continue;
                    }
                    $pageModeList = $moaagmDatabaseController->wordsRoot(trim($conditionWords[$j]));
                    $rootWord = !empty($pageModeList) ? $pageModeList[0]->name ?? '' : trim($conditionWords[$j]);
                    if (!empty($rootWord)) {
                        $conditionAsWordsRoot[] = $rootWord;
                    }
                }
                if (empty($conditionAsWordsRoot)) {
                    continue;
                }
                $conditionOperator = $searchFtsModel->conditionsOperators[$i];
                if ($conditionOperator == 'و') {
                    $whereQ .= ' "' . trim(implode(' ', $conditionAsWordsRoot)) . '"';
                }
                if ($conditionOperator == 'أو') {
                    $whereQ .= ' OR "' . trim(implode(' ', $conditionAsWordsRoot)) . '"';
                }
                if ($conditionOperator == 'ليس') {
                    $whereQ .= '-"' . trim(implode(' ', $conditionAsWordsRoot)) . '"';
                }
                if ($conditionOperator == 'قريب') {
                    $whereQ .= ' NEAR/5 "' . trim(implode(' ', $conditionAsWordsRoot)) . '"';
                }
                if ($conditionOperator == 'ليس') {
                    $whereQ = str_replace('"', '', $whereQ);
                }
            }
        }
        if (empty($whereColumn)) {
            if ($searchFtsModel->enableSearchInFooter) {
                $whereQ = "WHERE (search_root MATCH '$whereQ' OR search_footer_root MATCH '$whereQ')";
            } else {
                $whereQ = "WHERE search_root MATCH '$whereQ'";
            }
        } else {
            $whereQ = "WHERE {$whereColumn}_root MATCH '$whereQ'";
        }

        if (count($searchFtsModel->idsToExclude) > 0) {
            $whereQ .= ' AND (id NOT IN (' . implode(',', $searchFtsModel->idsToExclude) . '))';
        }

        if (count($searchFtsModel->bookIdsToExclude) > 0) {
            $whereQ .= ' AND (book_id NOT IN (' . implode(',', $searchFtsModel->bookIdsToExclude) . '))';
        }
        if (count($searchFtsModel->bookIdsToInclude) > 0) {
            $whereQ .= ' AND (book_id IN (' . implode(',', $searchFtsModel->bookIdsToInclude) . '))';
        }
        $whereQ = str_replace('xyz', '', $whereQ);
        return $whereQ;
    }

    public static function generateWhereQueriesForDefaultSearch($searchFtsModel, $whereColumn)
    {
        $search = trim($searchFtsModel->search);
        $whereQueries = ['"' . trim($search) . '"', trim($search), implode(' OR ', explode(' ', trim($search)))];

        // Handle أبو & ابا & ابو && ابن & بن
        $whereQueries = array_map(function ($e) {
            $words = explode(' ', $e);
            $sentence = '';
            foreach ($words as $word) {
                $word = trim($word);
                if (strpos($e, '"') !== false) {
                    $sentence .= ' ' . $word;
                } else {
                    $currentText = $sentence;
                    if ($word == 'ابي' || $word == 'ابا' || $word == 'ابو') {
                        // $sentence .= ' (ابو OR ابا OR ابي)';
                        $sentence .= $currentText . ' ' . 'ابو OR' . ' ' . $currentText . ' ' . 'ابا OR' . ' ' . $currentText . ' ' . 'ابي';
                    } elseif ($word == 'ابن' || $word == 'بن') {
                        // $sentence .= ' (ابن OR بن)';
                        $sentence .= $currentText . ' ' . 'ابن OR' . ' ' . $currentText . ' ' . 'بن';
                    } else {
                        $sentence .= ' ' . $word;
                    }
                }
            }
            return trim($sentence);
        }, $whereQueries);

        $returnQueries = [];

        foreach ($whereQueries as $whereQ) {
            if (!empty($searchFtsModel->conditions) && count($searchFtsModel->conditions) == count($searchFtsModel->conditionsOperators)) {
                for ($i = 0; $i < count($searchFtsModel->conditions); $i++) {
                    $conditionText = $searchFtsModel->conditions[$i];
                    if (trim($conditionText) == '') {
                        continue;
                    }
                    $conditionOperator = $searchFtsModel->conditionsOperators[$i];
                    if ($conditionOperator == 'و') {
                        $whereQ .= ' "' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'أو') {
                        $whereQ .= ' OR "' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'ليس') {
                        $whereQ .= '-"' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'قريب') {
                        $whereQ .= ' NEAR/5 "' . $conditionText . '"';
                    }
                    if ($conditionOperator == 'ليس') {
                        $whereQ = str_replace('"', '', $whereQ);
                    }
                }
            }
            if (empty($whereColumn)) {
                if ($searchFtsModel->enableSearchInFooter) {
                    $whereQ = "WHERE (search MATCH '$whereQ' OR search_footer MATCH '$whereQ')";
                } else {
                    $whereQ = "WHERE search MATCH ('$whereQ')";
                }
            } else {
                $whereQ = "WHERE $whereColumn MATCH '$whereQ'";
            }

            if (count($searchFtsModel->idsToExclude) > 0) {
                $whereQ .= ' AND (id NOT IN (' . implode(',', $searchFtsModel->idsToExclude) . '))';
            }

            if (count($searchFtsModel->bookIdsToExclude) > 0) {
                $whereQ .= ' AND (book_id NOT IN (' . implode(',', $searchFtsModel->bookIdsToExclude) . '))';
            }

            if (count($searchFtsModel->bookIdsToInclude) > 0) {
                $whereQ .= ' AND (book_id IN (' . implode(',', $searchFtsModel->bookIdsToInclude) . '))';
            }

            $whereQ = str_replace('xyz', '', $whereQ);
            $returnQueries[] = $whereQ;
        }

        return $returnQueries;
    }

    public static function searchResultsCount($term)
    {
        //handle SQL injection remove unwanted characters
        $term = preg_replace('/[^A-Za-z0-9\p{Arabic}]/u', ' ', $term);
        $term = preg_replace('/\s+/', ' ', $term);
        $term = trim($term);

        if ($term == '') {
            return [];
        }

        //check if empty search
        if (empty($term)) {
            return [];
        }

        $searchFtsModel = new SearchFtsModel($term, null, 10, 'افتراضي', false);

        $connection = 'sqlite_search';
        $db = DB::connection($connection);

        // For counting, we'll use a simpler approach
        $whereQueries = self::generateWhereQueriesForDefaultSearch($searchFtsModel, '');

        try {
            // Create a query that counts the total results across all search types
            $countQuery = 'SELECT SUM(result_count) as total_count FROM (';
            foreach ($whereQueries as $i => $whereQ) {
                $countQuery .= "SELECT COUNT(*) as result_count FROM books_search_fts $whereQ UNION ALL ";
            }
            $countQuery = substr($countQuery, 0, -11); // Remove the last " UNION ALL "
            $countQuery .= ')';

            $rows = $db->select($countQuery);
            Log::info('Count query: ' . $countQuery);
        } catch (Exception $e) {
            Log::error('Error: ' . $e->getMessage());
            Log::error('Query: ' . ($countQuery ?? 'No query available'));
            return 0;
        }

        if (empty($rows)) {
            return 0;
        }

        return $rows[0]->total_count ?? 0;
    }

    public static function searchResultsCountUniqueBooks($searchFtsModel)
    {
        $db = DB::connection('sqlite_search');
        $query = self::generateQuery($searchFtsModel, '');

        $query = str_replace('LIMIT ' . $searchFtsModel->limit, '', $query);
        $query = str_replace('offsets(books_search_fts),', '', $query);
        $countQuery = str_replace('SELECT *', 'SELECT COUNT(DISTINCT book_id)', $query);
        $countQuery = str_replace(' * ', ' id ,book_id ', $countQuery);
        $countQuery = str_replace('GROUP BY search', '', $countQuery);

        try {
            $rows = $db->select($countQuery);
        } catch (Exception $e) {
            // Handle error
            try {
                $queryForSearch = self::generateQuery($searchFtsModel, 'search');
                $queryForSearchFooter = self::generateQuery($searchFtsModel, 'search_footer');

                $queryForSearch = str_replace('LIMIT ' . $searchFtsModel->limit, '', $queryForSearch);
                $queryForSearch = str_replace('offsets(books_search_fts),', '', $queryForSearch);
                $queryForSearchFooter = str_replace('LIMIT ' . $searchFtsModel->limit, '', $queryForSearchFooter);
                $queryForSearchFooter = str_replace('offsets(books_search_fts),', '', $queryForSearchFooter);

                $countSearch = str_replace('SELECT *', 'SELECT COUNT(DISTINCT book_id)', $queryForSearch);
                $countSearch = str_replace(' * ', ' id ,book_id ', $countSearch);
                $countSearch = str_replace('GROUP BY search', '', $countSearch);

                $countSearchFooter = str_replace('SELECT *', 'SELECT COUNT(DISTINCT book_id)', $queryForSearchFooter);
                $countSearchFooter = str_replace(' * ', ' id ,book_id ', $countSearchFooter);
                $countSearchFooter = str_replace('GROUP BY search', '', $countSearchFooter);

                $results1 = $db->select($countSearch);
                $results2 = $db->select($countSearchFooter);

                $rows = array_merge($results1, $results2);
            } catch (Exception $e) {
                // Handle error
            }
        }

        if (empty($rows)) {
            return 0;
        }
        $count = isset($rows[0]->count) ? $rows[0]->count : 0;
        return $count;
    }

    /**
     * Count the number of search records for a specific book
     *
     * @param int $bookId
     * @return int
     */
    public static function countRecordsForBook($bookId)
    {
        $db = DB::connection('sqlite_search');
        try {
            $query = 'SELECT COUNT(*) as count FROM book_searches WHERE book_id = ?';
            $result = $db->select($query, [$bookId]);
            return isset($result[0]->count) ? $result[0]->count : 0;
        } catch (Exception $e) {
            Log::error('Error counting book search records: ' . $e->getMessage());
            return 0;
        }
    }
}
